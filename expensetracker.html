<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Tracker</title>
    <link rel="stylesheet" href="expensetrackers.css">
  </head>
  <body>
    <h2>Expense Tracker</h2>
    <div class="container">
      <h4>Your Balance</h4>
      <h1 id="balance">&#8377;0.00</h1>
      <div class="inc-exp-container">
        <div>
          <h4>Income</h4>
          <p id="money-plus" class="money-plus">
            +&#8377;0.00
          </p>
        </div>
        <div>
          <h4>Expense</h4>
          <p id="money-minus" class="money-minus">
            -&#8377;0.00
          </p>
        </div>
      </div>

      <h3>History</h3>
      <ul id="list" class="list">
        <li class="minus">
          ><button class="delete-btn">x</button>
        </li>
      </ul>
      <h3>Add New Transition</h3>
      <form id="form">
          <div class="form-control">
              <label for="text">Text</label>
              <input type="text" id="text" placeholder="Enter name"/>
          </div>
          <div class="form-control">
              <label for="amount">Amount <br> (negative - expense ,positive - income )</label>
              <input type="number" id="amount" placeholder="Enter amount"> 
          </div>
          <button class="btn">Submit</button>
      </form>
    </div>


    <script src="trackerscript.js"></script>
  </body>
</html>