<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Expense Tracker - Modern Financial Management</title>
    <link rel="stylesheet" href="expensetrackers.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container">
      <h2>💰 Expense Tracker</h2>

      <!-- Balance Display Card -->
      <div class="balance-card">
        <div class="balance-title">Your Balance</div>
        <h1 id="balance" class="balance-amount">₹0.00</h1>
      </div>

      <!-- Income/Expense Summary Cards -->
      <div class="inc-exp-container">
        <div class="summary-card income">
          <h4>Income</h4>
          <p id="money-plus" class="money money-plus">+₹0.00</p>
        </div>
        <div class="summary-card expense">
          <h4>Expense</h4>
          <p id="money-minus" class="money money-minus">-₹0.00</p>
        </div>
      </div>

      <!-- Transaction History -->
      <h3>📊 Transaction History</h3>
      <div class="transaction-container">
        <ul id="list" class="list">
          <!-- Transactions will be dynamically added here -->
        </ul>
        <div id="empty-state" class="empty-state" style="display: none">
          <div class="empty-state-icon">📝</div>
          <div class="empty-state-text">No transactions yet</div>
          <div class="empty-state-subtext">
            Add your first transaction below to get started
          </div>
        </div>
      </div>

      <!-- Add Transaction Form -->
      <h3>➕ Add New Transaction</h3>
      <div class="form-card">
        <form id="form">
          <div class="form-control">
            <label for="text">Description</label>
            <input
              type="text"
              id="text"
              placeholder="Enter transaction description"
              required
            />
          </div>
          <div class="form-control">
            <label for="amount">Amount (₹)</label>
            <input
              type="number"
              id="amount"
              placeholder="Enter amount (negative for expense, positive for income)"
              step="0.01"
              required
            />
          </div>
          <button type="submit" class="btn">Add Transaction</button>
        </form>
      </div>
    </div>

    <script src="trackerscript.js"></script>
  </body>
</html>
