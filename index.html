<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Personal Finance Manager</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
  </head>
  <body>
    <div class="app-container">
      <!-- Sidebar Navigation -->
      <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
          <div class="sidebar-toggle" id="sidebarToggle">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <h2 class="sidebar-title">💰 Finance Manager</h2>
        </div>

        <ul class="nav-menu">
          <li class="nav-item">
            <a href="#" class="nav-link active" data-page="overview">
              <span class="nav-icon">📊</span>
              <span class="nav-text">Overview</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link" data-page="income">
              <span class="nav-icon">💵</span>
              <span class="nav-text">Income</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link" data-page="expenses">
              <span class="nav-icon">💸</span>
              <span class="nav-text">Expenses</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link" data-page="investments">
              <span class="nav-icon">📈</span>
              <span class="nav-text">Investments</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link" data-page="budget">
              <span class="nav-icon">🎯</span>
              <span class="nav-text">Budget</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link" data-page="credit-import">
              <span class="nav-icon">💳</span>
              <span class="nav-text">Credit Card Import</span>
            </a>
          </li>
        </ul>
      </nav>

      <!-- Main Content Area -->
      <main class="main-content">
        <!-- Overview Page -->
        <div class="page active" id="overview-page">
          <div class="page-header">
            <h1>📊 Financial Overview</h1>
            <p>Your complete financial dashboard</p>
          </div>

          <!-- Key Metrics Cards -->
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-icon">💰</div>
              <div class="metric-content">
                <h3>Net Worth</h3>
                <p class="metric-value" id="netWorth">₹0.00</p>
                <span class="metric-change positive" id="netWorthChange"
                  >+0.00%</span
                >
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">💵</div>
              <div class="metric-content">
                <h3>Monthly Income</h3>
                <p class="metric-value" id="monthlyIncome">₹0.00</p>
                <span class="metric-change positive" id="incomeChange"
                  >+0.00%</span
                >
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">💸</div>
              <div class="metric-content">
                <h3>Monthly Expenses</h3>
                <p class="metric-value" id="monthlyExpenses">₹0.00</p>
                <span class="metric-change negative" id="expenseChange"
                  >+0.00%</span
                >
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">📈</div>
              <div class="metric-content">
                <h3>Total Investments</h3>
                <p class="metric-value" id="totalInvestments">₹0.00</p>
                <span class="metric-change positive" id="investmentChange"
                  >+0.00%</span
                >
              </div>
            </div>
          </div>

          <!-- Charts Section -->
          <div class="charts-grid">
            <div class="chart-card">
              <h3>Expense Breakdown</h3>
              <canvas id="expenseChart"></canvas>
            </div>
            <div class="chart-card">
              <h3>Income vs Expenses</h3>
              <canvas id="incomeExpenseChart"></canvas>
            </div>
            <div class="chart-card">
              <h3>Net Worth Trend</h3>
              <canvas id="netWorthChart"></canvas>
            </div>
            <div class="chart-card">
              <h3>Investment Portfolio</h3>
              <canvas id="portfolioChart"></canvas>
            </div>
          </div>

          <!-- Recent Transactions -->
          <div class="recent-transactions">
            <h3>Recent Transactions</h3>
            <div class="transaction-list" id="recentTransactions">
              <!-- Recent transactions will be populated here -->
            </div>
          </div>
        </div>

        <!-- Income Page -->
        <div class="page" id="income-page">
          <div class="page-header">
            <h1>💵 Income Management</h1>
            <p>Track and manage your income sources</p>
          </div>

          <!-- Income Analytics -->
          <div class="analytics-grid">
            <div class="analytics-card">
              <h4>Current Month</h4>
              <p class="analytics-value" id="currentMonthIncome">₹0.00</p>
            </div>
            <div class="analytics-card">
              <h4>Last Month</h4>
              <p class="analytics-value" id="lastMonthIncome">₹0.00</p>
            </div>
            <div class="analytics-card">
              <h4>Average Monthly</h4>
              <p class="analytics-value" id="avgMonthlyIncome">₹0.00</p>
            </div>
          </div>

          <!-- Add Income Form -->
          <div class="form-section">
            <h3>Add New Income</h3>
            <form id="incomeForm" class="transaction-form">
              <div class="form-row">
                <div class="form-group">
                  <label for="incomeSource">Income Source</label>
                  <input
                    type="text"
                    id="incomeSource"
                    placeholder="e.g., Salary, Freelance"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="incomeAmount">Amount (₹)</label>
                  <input
                    type="number"
                    id="incomeAmount"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="incomeCategory">Category</label>
                  <select id="incomeCategory" required>
                    <option value="">Select Category</option>
                    <option value="Salary">Salary</option>
                    <option value="Freelance">Freelance</option>
                    <option value="Business">Business</option>
                    <option value="Investment Returns">
                      Investment Returns
                    </option>
                    <option value="Rental">Rental</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="incomeDate">Date</label>
                  <input type="date" id="incomeDate" required />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group checkbox-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="recurringIncome" />
                    <span class="checkmark"></span>
                    Recurring Income
                  </label>
                </div>
              </div>
              <button type="submit" class="btn btn-primary">Add Income</button>
            </form>
          </div>

          <!-- Income History -->
          <div class="history-section">
            <div class="section-header">
              <h3>Income History</h3>
              <div class="filter-controls">
                <select id="incomeFilter">
                  <option value="All">All Categories</option>
                  <option value="Salary">Salary</option>
                  <option value="Freelance">Freelance</option>
                  <option value="Business">Business</option>
                  <option value="Investment Returns">Investment Returns</option>
                  <option value="Rental">Rental</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Source</th>
                    <th>Amount</th>
                    <th>Category</th>
                    <th>Date</th>
                    <th>Recurring</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="incomeList">
                  <!-- Income entries will be populated here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Expenses Page -->
        <div class="page" id="expenses-page">
          <div class="page-header">
            <h1>💸 Expense Tracking</h1>
            <p>Monitor and categorize your expenses</p>
          </div>

          <!-- Expense Analytics -->
          <div class="analytics-grid">
            <div class="analytics-card">
              <h4>This Month</h4>
              <p class="analytics-value" id="currentMonthExpenses">₹0.00</p>
            </div>
            <div class="analytics-card">
              <h4>Budget Remaining</h4>
              <p class="analytics-value" id="budgetRemaining">₹0.00</p>
            </div>
            <div class="analytics-card">
              <h4>Daily Average</h4>
              <p class="analytics-value" id="dailyAverage">₹0.00</p>
            </div>
          </div>

          <!-- Add Expense Form -->
          <div class="form-section">
            <h3>Add New Expense</h3>
            <form id="expenseForm" class="transaction-form">
              <div class="form-row">
                <div class="form-group">
                  <label for="expenseName">Expense Name</label>
                  <input
                    type="text"
                    id="expenseName"
                    placeholder="e.g., Groceries, Gas"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="expenseAmount">Amount (₹)</label>
                  <input
                    type="number"
                    id="expenseAmount"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="expenseCategory">Category</label>
                  <select id="expenseCategory" required>
                    <option value="">Select Category</option>
                    <option value="Food">Food</option>
                    <option value="Grocery">Grocery</option>
                    <option value="Transport">Transport</option>
                    <option value="Fuel">Fuel</option>
                    <option value="Entertainment">Entertainment</option>
                    <option value="Shopping">Shopping</option>
                    <option value="Bills">Bills</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Education">Education</option>
                    <option value="Travel">Travel</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="expenseDate">Date</label>
                  <input type="date" id="expenseDate" required />
                </div>
              </div>
              <button type="submit" class="btn btn-primary">Add Expense</button>
            </form>
          </div>

          <!-- Expense History -->
          <div class="history-section">
            <div class="section-header">
              <h3>Expense History</h3>
              <div class="filter-controls">
                <select id="expenseFilter">
                  <option value="All">All Categories</option>
                  <option value="Food">Food</option>
                  <option value="Grocery">Grocery</option>
                  <option value="Transport">Transport</option>
                  <option value="Fuel">Fuel</option>
                  <option value="Entertainment">Entertainment</option>
                  <option value="Shopping">Shopping</option>
                  <option value="Bills">Bills</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Education">Education</option>
                  <option value="Travel">Travel</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <!-- Filtered Total Display - Moved below filter dropdown -->
              <div class="filtered-total-section">
                <div class="filtered-total-display">
                  <span class="filtered-total-label" id="expenseFilteredLabel"
                    >Total:</span
                  >
                  <span class="filtered-total-amount" id="expenseFilteredTotal"
                    >₹0.00</span
                  >
                </div>
              </div>
            </div>

            <!-- Bulk Selection Controls - Moved below heading, above table -->
            <div class="bulk-controls-section">
              <div class="bulk-controls">
                <button
                  class="btn btn-secondary"
                  onclick="financeManager.selectAllExpenses()"
                >
                  Select All
                </button>
                <button
                  class="btn btn-secondary"
                  onclick="financeManager.deselectAllExpenses()"
                >
                  Deselect All
                </button>
                <button
                  class="btn btn-danger"
                  id="deleteSelectedBtn"
                  onclick="financeManager.deleteSelectedExpenses()"
                  style="display: none"
                >
                  Delete Selected
                </button>
              </div>
            </div>

            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Select</th>
                    <th>Name</th>
                    <th>Amount</th>
                    <th>Category</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="expenseList">
                  <!-- Expense entries will be populated here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Investments Page -->
        <div class="page" id="investments-page">
          <div class="page-header">
            <h1>📈 Investment Portfolio</h1>
            <p>Track your investment performance</p>
          </div>

          <!-- Investment Analytics -->
          <div class="analytics-grid">
            <div class="analytics-card">
              <h4>Total Portfolio Value</h4>
              <p class="analytics-value" id="portfolioValue">₹0.00</p>
            </div>
            <div class="analytics-card">
              <h4>Total Gains/Losses</h4>
              <p class="analytics-value" id="totalGains">₹0.00</p>
            </div>
            <div class="analytics-card">
              <h4>Overall Return</h4>
              <p class="analytics-value" id="overallReturn">0.00%</p>
            </div>
          </div>

          <!-- Add Investment Form -->
          <div class="form-section">
            <h3>Add New Investment</h3>
            <form id="investmentForm" class="transaction-form">
              <div class="form-row">
                <div class="form-group">
                  <label for="investmentName">Investment Name</label>
                  <input
                    type="text"
                    id="investmentName"
                    placeholder="e.g., HDFC Mutual Fund"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="investmentType">Type</label>
                  <select id="investmentType" required>
                    <option value="">Select Type</option>
                    <option value="Stocks">Stocks</option>
                    <option value="Mutual Funds">Mutual Funds</option>
                    <option value="Fixed Deposits">Fixed Deposits</option>
                    <option value="Crypto">Crypto</option>
                    <option value="Bonds">Bonds</option>
                    <option value="Real Estate">Real Estate</option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="investmentAmount">Investment Amount (₹)</label>
                  <input
                    type="number"
                    id="investmentAmount"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
                <div class="form-group">
                  <label for="currentValue">Current Value (₹)</label>
                  <input
                    type="number"
                    id="currentValue"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label for="investmentDate">Investment Date</label>
                  <input type="date" id="investmentDate" required />
                </div>
              </div>
              <button type="submit" class="btn btn-primary">
                Add Investment
              </button>
            </form>
          </div>

          <!-- Investment Portfolio -->
          <div class="history-section">
            <div class="section-header">
              <h3>Investment Portfolio</h3>
              <div class="filter-controls">
                <select id="investmentFilter">
                  <option value="All">All Types</option>
                  <option value="Stocks">Stocks</option>
                  <option value="Mutual Funds">Mutual Funds</option>
                  <option value="Fixed Deposits">Fixed Deposits</option>
                  <option value="Crypto">Crypto</option>
                  <option value="Bonds">Bonds</option>
                  <option value="Real Estate">Real Estate</option>
                </select>
              </div>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Investment</th>
                    <th>Current Value</th>
                    <th>Gain/Loss</th>
                    <th>Return %</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="investmentList">
                  <!-- Investment entries will be populated here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Budget Page -->
        <div class="page" id="budget-page">
          <div class="page-header">
            <h1>🎯 Budget Management</h1>
            <p>Set and track your monthly budgets</p>
          </div>

          <!-- Budget Overview -->
          <div class="budget-overview">
            <div class="budget-card">
              <h3>Monthly Budget</h3>
              <div class="budget-amount">
                <span id="monthlyBudget">₹0.00</span>
                <button class="btn btn-secondary" id="setBudgetBtn">
                  Set Budget
                </button>
              </div>
              <div class="budget-progress">
                <div class="progress-bar">
                  <div class="progress-fill" id="budgetProgress"></div>
                </div>
                <span class="progress-text" id="budgetProgressText"
                  >0% used</span
                >
              </div>
            </div>
          </div>

          <!-- Category Budgets -->
          <div class="category-budgets">
            <h3>Category Budgets</h3>
            <div class="budget-grid" id="categoryBudgets">
              <!-- Category budget cards will be populated here -->
            </div>
          </div>

          <!-- Set Budget Modal -->
          <div class="modal" id="budgetModal">
            <div class="modal-content">
              <div class="modal-header">
                <h3>Set Monthly Budget</h3>
                <span class="close" id="closeBudgetModal">&times;</span>
              </div>
              <form id="budgetForm">
                <div class="form-group">
                  <label for="budgetAmount">Monthly Budget Amount (₹)</label>
                  <input
                    type="number"
                    id="budgetAmount"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                    required
                  />
                </div>
                <button type="submit" class="btn btn-primary">
                  Set Budget
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Credit Card Import Page -->
        <div class="page" id="credit-import-page">
          <div class="page-header">
            <h1>💳 Credit Card Import</h1>
            <p>Import transactions from your credit card statements</p>
          </div>

          <!-- Upload Section -->
          <div class="upload-section">
            <div class="upload-card">
              <div class="upload-icon">📄</div>
              <h3>Upload Credit Card Statement</h3>
              <p>Select a PDF file containing your credit card statement</p>

              <div class="file-upload-area" id="fileUploadArea">
                <input
                  type="file"
                  id="pdfFileInput"
                  accept=".pdf"
                  style="display: none"
                />
                <div class="upload-placeholder" id="uploadPlaceholder">
                  <div class="upload-icon-large">📁</div>
                  <p>Click to select PDF file or drag and drop</p>
                  <span class="file-types">Supports: PDF files only</span>
                </div>
                <div
                  class="file-selected"
                  id="fileSelected"
                  style="display: none"
                >
                  <div class="file-info">
                    <span class="file-name" id="fileName"></span>
                    <span class="file-size" id="fileSize"></span>
                  </div>
                  <button class="btn btn-secondary" id="changeFileBtn">
                    Change File
                  </button>
                </div>
              </div>

              <!-- Password Input for Protected PDFs -->
              <div
                class="password-section"
                id="passwordSection"
                style="display: none"
              >
                <div class="form-group">
                  <label for="pdfPassword">PDF Password (if protected)</label>
                  <input
                    type="password"
                    id="pdfPassword"
                    placeholder="Enter PDF password"
                  />
                </div>
              </div>

              <!-- Processing Controls -->
              <div class="processing-controls">
                <button class="btn btn-primary" id="processPdfBtn" disabled>
                  <span class="btn-text">Process PDF</span>
                  <span class="btn-loader" style="display: none">⏳</span>
                </button>
                <div class="processing-status" id="processingStatus"></div>
              </div>
            </div>
          </div>

          <!-- Preview Section -->
          <div
            class="preview-section"
            id="previewSection"
            style="display: none"
          >
            <h3>Extracted Transactions</h3>
            <p>
              Review and categorize the extracted transactions before importing
            </p>

            <div class="preview-controls">
              <button class="btn btn-success" id="importAllBtn">
                Import All
              </button>
              <button class="btn btn-secondary" id="selectAllBtn">
                Select All
              </button>
              <button class="btn btn-secondary" id="deselectAllBtn">
                Deselect All
              </button>
            </div>

            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>
                      <input type="checkbox" id="selectAllCheckbox" />
                    </th>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Amount</th>
                    <th>Category</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody id="extractedTransactions">
                  <!-- Extracted transactions will be populated here -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Import History -->
          <div class="import-history">
            <h3>Import History</h3>
            <div class="history-list" id="importHistory">
              <!-- Import history will be populated here -->
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Success/Error Messages -->
    <div class="toast" id="toast">
      <div class="toast-content">
        <span class="toast-icon" id="toastIcon"></span>
        <span class="toast-message" id="toastMessage"></span>
      </div>
    </div>

    <script src="app.js"></script>
  </body>
</html>
