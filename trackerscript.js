// Modern Expense Tracker - Enhanced JavaScript
// DOM Elements
const balance = document.getElementById("balance");
const money_plus = document.getElementById("money-plus");
const money_minus = document.getElementById("money-minus");
const list = document.getElementById("list");
const form = document.getElementById("form");
const text = document.getElementById("text");
const amount = document.getElementById("amount");
const emptyState = document.getElementById("empty-state");

// Load transactions from localStorage
const localStorageTransactions = JSON.parse(
  localStorage.getItem("transactions")
);
let transactions =
  localStorage.getItem("transactions") !== null ? localStorageTransactions : [];

// Utility Functions
function formatCurrency(amount) {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 2,
  }).format(Math.abs(amount));
}

function showEmptyState() {
  if (transactions.length === 0) {
    emptyState.style.display = "block";
    list.style.display = "none";
  } else {
    emptyState.style.display = "none";
    list.style.display = "block";
  }
}

function addFadeInAnimation(element) {
  element.classList.add("fade-in");
  setTimeout(() => {
    element.classList.remove("fade-in");
  }, 500);
}

function animateBalance() {
  balance.classList.add("balance-updated");
  setTimeout(() => {
    balance.classList.remove("balance-updated");
  }, 300);
}

// Add Transaction Function
function addTransaction(e) {
  e.preventDefault();

  // Validate inputs
  if (text.value.trim() === "" || amount.value.trim() === "") {
    showMessage("Please enter both description and amount", "error");
    return;
  }

  if (amount.value == 0) {
    showMessage("Amount cannot be zero", "error");
    return;
  }

  const transaction = {
    id: generateID(),
    text: text.value.trim(),
    amount: +amount.value,
    date: new Date().toISOString(),
  };

  transactions.push(transaction);

  addTransactionDOM(transaction);
  updateValues();
  updateLocalStorage();
  showEmptyState();

  // Clear form
  text.value = "";
  amount.value = "";

  // Show success message
  showMessage("Transaction added successfully!", "success");

  // Animate balance
  animateBalance();
}

// Generate Random ID
function generateID() {
  return Math.floor(Math.random() * 1000000000);
}

// Add Transaction to DOM
function addTransactionDOM(transaction) {
  const item = document.createElement("li");

  // Add appropriate class based on transaction type
  item.classList.add(transaction.amount < 0 ? "minus" : "plus");

  // Create transaction HTML with modern structure
  item.innerHTML = `
    <div class="transaction-text">${transaction.text}</div>
    <div class="transaction-amount">${
      transaction.amount < 0 ? "-" : "+"
    }${formatCurrency(transaction.amount)}</div>
    <button class="delete-btn" onclick="removeTransaction(${
      transaction.id
    })">×</button>
  `;

  // Add fade-in animation
  addFadeInAnimation(item);

  list.appendChild(item);
}

// Show success/error messages
function showMessage(message, type) {
  // Remove existing messages
  const existingMessage = document.querySelector(
    ".success-message, .error-message"
  );
  if (existingMessage) {
    existingMessage.remove();
  }

  const messageDiv = document.createElement("div");
  messageDiv.className =
    type === "success" ? "success-message" : "error-message";
  messageDiv.textContent = message;

  // Insert message before the form
  const formCard = document.querySelector(".form-card");
  formCard.parentNode.insertBefore(messageDiv, formCard);

  // Auto-remove message after 3 seconds
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.remove();
    }
  }, 3000);
}
//4

// Update the balance, income and expense displays
function updateValues() {
  const amounts = transactions.map((transaction) => transaction.amount);

  const total = amounts.reduce((acc, item) => (acc += item), 0);
  const income = amounts
    .filter((item) => item > 0)
    .reduce((acc, item) => (acc += item), 0);
  const expense =
    amounts.filter((item) => item < 0).reduce((acc, item) => (acc += item), 0) *
    -1;

  // Update balance with proper formatting
  balance.innerHTML = `₹${total.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;

  // Update income and expense with proper formatting
  money_plus.innerHTML = `+₹${income.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
  money_minus.innerHTML = `-₹${expense.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;

  // Update balance color based on total
  if (total >= 0) {
    balance.style.color = "var(--success-color)";
  } else {
    balance.style.color = "var(--danger-color)";
  }
}

//6

// Remove Transaction by ID
function removeTransaction(id) {
  const transactionIndex = transactions.findIndex(
    (transaction) => transaction.id === id
  );

  if (transactionIndex > -1) {
    const removedTransaction = transactions[transactionIndex];
    transactions = transactions.filter((transaction) => transaction.id !== id);
    updateLocalStorage();
    Init();
    showEmptyState();

    // Show success message
    showMessage(
      `Transaction "${removedTransaction.text}" removed successfully!`,
      "success"
    );

    // Animate balance
    animateBalance();
  }
}

// Update Local Storage Transaction
function updateLocalStorage() {
  localStorage.setItem("transactions", JSON.stringify(transactions));
}

// Initialize App
function Init() {
  list.innerHTML = "";
  transactions.forEach(addTransactionDOM);
  updateValues();
  showEmptyState();
}

// Initialize the app when page loads
Init();

// Event Listeners
form.addEventListener("submit", addTransaction);

// Add keyboard shortcuts
document.addEventListener("keydown", function (e) {
  // Ctrl/Cmd + Enter to submit form
  if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
    e.preventDefault();
    if (text.value.trim() && amount.value.trim()) {
      addTransaction(e);
    }
  }
});
