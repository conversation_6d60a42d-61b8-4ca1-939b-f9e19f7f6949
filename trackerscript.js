// Modern Expense Tracker - Enhanced JavaScript with Categories and Editing
// DOM Elements
const balance = document.getElementById("balance");
const money_plus = document.getElementById("money-plus");
const money_minus = document.getElementById("money-minus");
const list = document.getElementById("list");
const form = document.getElementById("form");
const text = document.getElementById("text");
const amount = document.getElementById("amount");
const category = document.getElementById("category");
const date = document.getElementById("date");
const categoryFilter = document.getElementById("categoryFilter");
const totalAmount = document.getElementById("totalAmount");
const emptyState = document.getElementById("empty-state");

// Set default date to today
date.valueAsDate = new Date();

// Global variables
let editingTransactionId = null;

// Load transactions from localStorage
const localStorageTransactions = JSON.parse(
  localStorage.getItem("transactions")
);
let transactions =
  localStorage.getItem("transactions") !== null ? localStorageTransactions : [];

// Utility Functions
function formatCurrency(amount) {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 2,
  }).format(Math.abs(amount));
}

function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-IN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}

function getCategoryBadgeClass(category) {
  return `category-${category.toLowerCase()}`;
}

function showEmptyState() {
  const filteredTransactions = getFilteredTransactions();
  if (filteredTransactions.length === 0) {
    emptyState.style.display = "block";
    document.querySelector(".table-container table").style.display = "none";
  } else {
    emptyState.style.display = "none";
    document.querySelector(".table-container table").style.display = "table";
  }
}

function getFilteredTransactions() {
  const filterValue = categoryFilter.value;
  if (filterValue === "All") {
    return transactions;
  }
  return transactions.filter(
    (transaction) => transaction.category === filterValue
  );
}

function addFadeInAnimation(element) {
  element.classList.add("fade-in");
  setTimeout(() => {
    element.classList.remove("fade-in");
  }, 500);
}

function animateBalance() {
  balance.classList.add("balance-updated");
  setTimeout(() => {
    balance.classList.remove("balance-updated");
  }, 300);
}

// Add/Edit Transaction Function
function addTransaction(e) {
  e.preventDefault();

  // Validate inputs
  if (
    text.value.trim() === "" ||
    amount.value.trim() === "" ||
    category.value === "" ||
    date.value === ""
  ) {
    showMessage("Please fill in all fields", "error");
    return;
  }

  if (amount.value <= 0) {
    showMessage("Amount must be greater than zero", "error");
    return;
  }

  const transaction = {
    id: editingTransactionId || generateID(),
    text: text.value.trim(),
    amount: +amount.value,
    category: category.value,
    date: date.value,
  };

  if (editingTransactionId) {
    // Update existing transaction
    const index = transactions.findIndex((t) => t.id === editingTransactionId);
    if (index !== -1) {
      transactions[index] = transaction;
      showMessage("Expense updated successfully!", "success");
    }
    editingTransactionId = null;
    document.querySelector(".btn").textContent = "Add Expense";
  } else {
    // Add new transaction
    transactions.push(transaction);
    showMessage("Expense added successfully!", "success");
  }

  updateLocalStorage();
  renderTransactions();
  updateValues();
  showEmptyState();

  // Clear form
  clearForm();

  // Animate balance
  animateBalance();
}

function clearForm() {
  text.value = "";
  amount.value = "";
  category.value = "";
  date.valueAsDate = new Date();
  editingTransactionId = null;
  document.querySelector(".btn").textContent = "Add Expense";
}

// Generate Random ID
function generateID() {
  return Math.floor(Math.random() * 1000000000);
}

// Render all transactions (replaces addTransactionDOM)
function renderTransactions() {
  const filteredTransactions = getFilteredTransactions();
  list.innerHTML = "";

  filteredTransactions.forEach((transaction) => {
    const row = document.createElement("tr");
    row.innerHTML = `
      <td>${transaction.text}</td>
      <td class="amount-display">₹${transaction.amount.toLocaleString("en-IN", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })}</td>
      <td><span class="category-badge ${getCategoryBadgeClass(
        transaction.category
      )}">${transaction.category}</span></td>
      <td class="date-display">${formatDate(transaction.date)}</td>
      <td>
        <div class="action-buttons">
          <button class="edit-btn" onclick="editTransaction(${
            transaction.id
          })">Edit</button>
          <button class="delete-btn" onclick="removeTransaction(${
            transaction.id
          })">Delete</button>
        </div>
      </td>
    `;

    // Add fade-in animation
    addFadeInAnimation(row);

    list.appendChild(row);
  });

  updateTotalDisplay();
}

// Edit transaction function
function editTransaction(id) {
  const transaction = transactions.find((t) => t.id === id);
  if (transaction) {
    text.value = transaction.text;
    amount.value = transaction.amount;
    category.value = transaction.category;
    date.value = transaction.date;
    editingTransactionId = id;
    document.querySelector(".btn").textContent = "Update Expense";

    // Scroll to form
    document.querySelector(".form-card").scrollIntoView({ behavior: "smooth" });
  }
}

// Update total display
function updateTotalDisplay() {
  const filteredTransactions = getFilteredTransactions();
  const total = filteredTransactions.reduce(
    (sum, transaction) => sum + transaction.amount,
    0
  );
  totalAmount.textContent = `₹${total.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
}

// Show success/error messages
function showMessage(message, type) {
  // Remove existing messages
  const existingMessage = document.querySelector(
    ".success-message, .error-message"
  );
  if (existingMessage) {
    existingMessage.remove();
  }

  const messageDiv = document.createElement("div");
  messageDiv.className =
    type === "success" ? "success-message" : "error-message";
  messageDiv.textContent = message;

  // Insert message before the form
  const formCard = document.querySelector(".form-card");
  formCard.parentNode.insertBefore(messageDiv, formCard);

  // Auto-remove message after 3 seconds
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.remove();
    }
  }, 3000);
}
//4

// Update the balance and expense displays (now expense-focused)
function updateValues() {
  const totalExpenses = transactions.reduce(
    (sum, transaction) => sum + transaction.amount,
    0
  );

  // For this expense tracker, we'll show total expenses and assume a starting balance
  const startingBalance = 10000; // You can make this configurable
  const currentBalance = startingBalance - totalExpenses;

  // Update balance with proper formatting
  balance.innerHTML = `₹${currentBalance.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;

  // Update displays - now showing starting amount and total expenses
  money_plus.innerHTML = `+₹${startingBalance.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
  money_minus.innerHTML = `-₹${totalExpenses.toLocaleString("en-IN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;

  // Update balance color based on remaining balance
  if (currentBalance >= 0) {
    balance.style.color = "var(--success-color)";
  } else {
    balance.style.color = "var(--danger-color)";
  }
}

//6

// Remove Transaction by ID
function removeTransaction(id) {
  const transactionIndex = transactions.findIndex(
    (transaction) => transaction.id === id
  );

  if (transactionIndex > -1) {
    const removedTransaction = transactions[transactionIndex];
    transactions = transactions.filter((transaction) => transaction.id !== id);
    updateLocalStorage();
    renderTransactions();
    updateValues();
    showEmptyState();

    // Show success message
    showMessage(
      `Expense "${removedTransaction.text}" removed successfully!`,
      "success"
    );

    // Animate balance
    animateBalance();

    // Clear form if we were editing this transaction
    if (editingTransactionId === id) {
      clearForm();
    }
  }
}

// Update Local Storage Transaction
function updateLocalStorage() {
  localStorage.setItem("transactions", JSON.stringify(transactions));
}

// Initialize App
function Init() {
  renderTransactions();
  updateValues();
  showEmptyState();
}

// Filter transactions by category
function filterTransactions() {
  renderTransactions();
  showEmptyState();
}

// Initialize the app when page loads
Init();

// Event Listeners
form.addEventListener("submit", addTransaction);
categoryFilter.addEventListener("change", filterTransactions);

// Add keyboard shortcuts
document.addEventListener("keydown", function (e) {
  // Ctrl/Cmd + Enter to submit form
  if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
    e.preventDefault();
    if (
      text.value.trim() &&
      amount.value.trim() &&
      category.value &&
      date.value
    ) {
      addTransaction(e);
    }
  }

  // Escape to cancel editing
  if (e.key === "Escape" && editingTransactionId) {
    clearForm();
  }
});
