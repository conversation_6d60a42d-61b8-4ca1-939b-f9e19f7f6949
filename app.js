// Personal Finance Manager - Comprehensive Application with Credit Card Import
// Initialize PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc =
  "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";

class FinanceManager {
  constructor() {
    this.data = {
      income: JSON.parse(localStorage.getItem("finance_income") || "[]"),
      expenses: JSON.parse(localStorage.getItem("finance_expenses") || "[]"),
      investments: JSON.parse(
        localStorage.getItem("finance_investments") || "[]"
      ),
      budget: JSON.parse(localStorage.getItem("finance_budget") || "{}"),
      importHistory: JSON.parse(
        localStorage.getItem("finance_import_history") || "[]"
      ),
    };

    this.currentPage = "overview";
    this.charts = {};
    this.extractedTransactions = [];

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupSidebar();
    this.setupPDFProcessor();
    this.loadPage("overview");
    this.updateAllMetrics();
    this.setDefaultDates();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        this.loadPage(page);
      });
    });

    // Forms
    document
      .getElementById("incomeForm")
      .addEventListener("submit", (e) => this.handleIncomeSubmit(e));
    document
      .getElementById("expenseForm")
      .addEventListener("submit", (e) => this.handleExpenseSubmit(e));
    document
      .getElementById("investmentForm")
      .addEventListener("submit", (e) => this.handleInvestmentSubmit(e));
    document
      .getElementById("budgetForm")
      .addEventListener("submit", (e) => this.handleBudgetSubmit(e));

    // Filters
    document
      .getElementById("incomeFilter")
      .addEventListener("change", () => this.renderIncomeList());
    document
      .getElementById("expenseFilter")
      .addEventListener("change", () => this.renderExpenseList());
    document
      .getElementById("investmentFilter")
      .addEventListener("change", () => this.renderInvestmentList());

    // Budget modal
    document
      .getElementById("setBudgetBtn")
      .addEventListener("click", () => this.openBudgetModal());
    document
      .getElementById("closeBudgetModal")
      .addEventListener("click", () => this.closeBudgetModal());

    // PDF Import
    document
      .getElementById("pdfFileInput")
      .addEventListener("change", (e) => this.handleFileSelect(e));
    document
      .getElementById("processPdfBtn")
      .addEventListener("click", () => this.processPDF());
    document
      .getElementById("importAllBtn")
      .addEventListener("click", () => this.importSelectedTransactions());
    document
      .getElementById("selectAllBtn")
      .addEventListener("click", () => this.selectAllTransactions());
    document
      .getElementById("deselectAllBtn")
      .addEventListener("click", () => this.deselectAllTransactions());
    document
      .getElementById("changeFileBtn")
      .addEventListener("click", () => this.changeFile());

    // File upload area
    const uploadArea = document.getElementById("fileUploadArea");
    uploadArea.addEventListener("click", () =>
      document.getElementById("pdfFileInput").click()
    );
    uploadArea.addEventListener("dragover", (e) => this.handleDragOver(e));
    uploadArea.addEventListener("drop", (e) => this.handleDrop(e));
    uploadArea.addEventListener("dragleave", (e) => this.handleDragLeave(e));
  }

  setupSidebar() {
    const sidebar = document.getElementById("sidebar");
    const sidebarToggle = document.getElementById("sidebarToggle");

    sidebarToggle.addEventListener("click", () => {
      sidebar.classList.toggle("collapsed");
    });

    // Mobile responsiveness
    if (window.innerWidth <= 1024) {
      sidebar.classList.add("collapsed");
    }

    window.addEventListener("resize", () => {
      if (window.innerWidth <= 1024) {
        sidebar.classList.add("mobile-open");
      } else {
        sidebar.classList.remove("mobile-open");
      }
    });
  }

  setupPDFProcessor() {
    // Initialize PDF processing variables
    this.selectedFile = null;
    this.pdfDocument = null;
  }

  setDefaultDates() {
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("incomeDate").value = today;
    document.getElementById("expenseDate").value = today;
    document.getElementById("investmentDate").value = today;
  }

  loadPage(pageName) {
    // Hide all pages
    document.querySelectorAll(".page").forEach((page) => {
      page.classList.remove("active");
    });

    // Show selected page
    document.getElementById(`${pageName}-page`).classList.add("active");

    // Update navigation
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.classList.remove("active");
    });
    document.querySelector(`[data-page="${pageName}"]`).classList.add("active");

    this.currentPage = pageName;

    // Load page-specific content
    switch (pageName) {
      case "overview":
        this.loadOverviewPage();
        break;
      case "income":
        this.loadIncomePage();
        break;
      case "expenses":
        this.loadExpensePage();
        break;
      case "investments":
        this.loadInvestmentPage();
        break;
      case "budget":
        this.loadBudgetPage();
        break;
      case "credit-import":
        this.loadCreditImportPage();
        break;
    }
  }

  // Data Management Methods
  saveData() {
    localStorage.setItem("finance_income", JSON.stringify(this.data.income));
    localStorage.setItem(
      "finance_expenses",
      JSON.stringify(this.data.expenses)
    );
    localStorage.setItem(
      "finance_investments",
      JSON.stringify(this.data.investments)
    );
    localStorage.setItem("finance_budget", JSON.stringify(this.data.budget));
    localStorage.setItem(
      "finance_import_history",
      JSON.stringify(this.data.importHistory)
    );
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }

  showToast(message, type = "info") {
    const toast = document.getElementById("toast");
    const toastIcon = document.getElementById("toastIcon");
    const toastMessage = document.getElementById("toastMessage");

    const icons = {
      success: "✅",
      error: "❌",
      info: "ℹ️",
      warning: "⚠️",
    };

    toast.className = `toast ${type}`;
    toastIcon.textContent = icons[type] || icons.info;
    toastMessage.textContent = message;

    toast.classList.add("show");

    setTimeout(() => {
      toast.classList.remove("show");
    }, 3000);
  }

  // Form Handlers
  handleIncomeSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const income = {
      id: this.generateId(),
      source: document.getElementById("incomeSource").value,
      amount: parseFloat(document.getElementById("incomeAmount").value),
      category: document.getElementById("incomeCategory").value,
      date: document.getElementById("incomeDate").value,
      recurring: document.getElementById("recurringIncome").checked,
      timestamp: new Date().toISOString(),
    };

    this.data.income.push(income);
    this.saveData();
    this.renderIncomeList();
    this.updateAllMetrics();
    this.showToast("Income added successfully!", "success");
    e.target.reset();
    this.setDefaultDates();
  }

  handleExpenseSubmit(e) {
    e.preventDefault();

    const expense = {
      id: this.generateId(),
      name: document.getElementById("expenseName").value,
      amount: parseFloat(document.getElementById("expenseAmount").value),
      category: document.getElementById("expenseCategory").value,
      date: document.getElementById("expenseDate").value,
      timestamp: new Date().toISOString(),
    };

    this.data.expenses.push(expense);
    this.saveData();
    this.renderExpenseList();
    this.updateAllMetrics();
    this.showToast("Expense added successfully!", "success");
    e.target.reset();
    this.setDefaultDates();
  }

  handleInvestmentSubmit(e) {
    e.preventDefault();

    const investment = {
      id: this.generateId(),
      name: document.getElementById("investmentName").value,
      type: document.getElementById("investmentType").value,
      amount: parseFloat(document.getElementById("investmentAmount").value),
      currentValue: parseFloat(document.getElementById("currentValue").value),
      date: document.getElementById("investmentDate").value,
      timestamp: new Date().toISOString(),
    };

    this.data.investments.push(investment);
    this.saveData();
    this.renderInvestmentList();
    this.updateAllMetrics();
    this.showToast("Investment added successfully!", "success");
    e.target.reset();
    this.setDefaultDates();
  }

  handleBudgetSubmit(e) {
    e.preventDefault();

    const budgetAmount = parseFloat(
      document.getElementById("budgetAmount").value
    );
    this.data.budget.monthly = budgetAmount;
    this.data.budget.setDate = new Date().toISOString();

    this.saveData();
    this.closeBudgetModal();
    this.updateBudgetDisplay();
    this.showToast("Budget set successfully!", "success");
  }

  // Delete Methods
  deleteIncome(id) {
    if (confirm("Are you sure you want to delete this income entry?")) {
      this.data.income = this.data.income.filter((item) => item.id !== id);
      this.saveData();
      this.renderIncomeList();
      this.updateAllMetrics();
      this.showToast("Income deleted successfully!", "success");
    }
  }

  deleteExpense(id) {
    if (confirm("Are you sure you want to delete this expense?")) {
      this.data.expenses = this.data.expenses.filter((item) => item.id !== id);
      this.saveData();
      this.renderExpenseList();
      this.updateAllMetrics();
      this.showToast("Expense deleted successfully!", "success");
    }
  }

  deleteInvestment(id) {
    if (confirm("Are you sure you want to delete this investment?")) {
      this.data.investments = this.data.investments.filter(
        (item) => item.id !== id
      );
      this.saveData();
      this.renderInvestmentList();
      this.updateAllMetrics();
      this.showToast("Investment deleted successfully!", "success");
    }
  }

  // Page Loading Methods
  loadOverviewPage() {
    this.updateAllMetrics();
    this.renderCharts();
    this.renderRecentTransactions();
  }

  loadIncomePage() {
    this.updateIncomeAnalytics();
    this.renderIncomeList();
  }

  loadExpensePage() {
    this.updateExpenseAnalytics();
    this.renderExpenseList();
  }

  loadInvestmentPage() {
    this.updateInvestmentAnalytics();
    this.renderInvestmentList();
  }

  loadBudgetPage() {
    this.updateBudgetDisplay();
    this.renderCategoryBudgets();
  }

  loadCreditImportPage() {
    this.renderImportHistory();
  }

  // Metrics and Analytics
  updateAllMetrics() {
    const totalIncome = this.data.income.reduce(
      (sum, item) => sum + item.amount,
      0
    );
    const totalExpenses = this.data.expenses.reduce(
      (sum, item) => sum + item.amount,
      0
    );
    const totalInvestments = this.data.investments.reduce(
      (sum, item) => sum + item.currentValue,
      0
    );
    const netWorth = totalIncome - totalExpenses + totalInvestments;

    // Current month calculations
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyIncome = this.data.income
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const monthlyExpenses = this.data.expenses
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    // Update UI
    document.getElementById("netWorth").textContent =
      this.formatCurrency(netWorth);
    document.getElementById("monthlyIncome").textContent =
      this.formatCurrency(monthlyIncome);
    document.getElementById("monthlyExpenses").textContent =
      this.formatCurrency(monthlyExpenses);
    document.getElementById("totalInvestments").textContent =
      this.formatCurrency(totalInvestments);

    // Update change indicators (simplified for now)
    document.getElementById("netWorthChange").textContent = "+0.00%";
    document.getElementById("incomeChange").textContent = "+0.00%";
    document.getElementById("expenseChange").textContent = "+0.00%";
    document.getElementById("investmentChange").textContent = "+0.00%";
  }

  updateIncomeAnalytics() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    const currentMonthIncome = this.data.income
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const lastMonthIncome = this.data.income
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === lastMonth &&
          itemDate.getFullYear() === lastMonthYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const avgMonthlyIncome =
      this.data.income.length > 0
        ? this.data.income.reduce((sum, item) => sum + item.amount, 0) /
          Math.max(1, this.getMonthsCount())
        : 0;

    document.getElementById("currentMonthIncome").textContent =
      this.formatCurrency(currentMonthIncome);
    document.getElementById("lastMonthIncome").textContent =
      this.formatCurrency(lastMonthIncome);
    document.getElementById("avgMonthlyIncome").textContent =
      this.formatCurrency(avgMonthlyIncome);
  }

  updateExpenseAnalytics() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const currentMonthExpenses = this.data.expenses
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const monthlyBudget = this.data.budget.monthly || 0;
    const budgetRemaining = monthlyBudget - currentMonthExpenses;
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const dailyAverage = currentMonthExpenses / new Date().getDate();

    document.getElementById("currentMonthExpenses").textContent =
      this.formatCurrency(currentMonthExpenses);
    document.getElementById("budgetRemaining").textContent =
      this.formatCurrency(budgetRemaining);
    document.getElementById("dailyAverage").textContent =
      this.formatCurrency(dailyAverage);
  }

  updateInvestmentAnalytics() {
    const totalInvestment = this.data.investments.reduce(
      (sum, item) => sum + item.amount,
      0
    );
    const totalCurrentValue = this.data.investments.reduce(
      (sum, item) => sum + item.currentValue,
      0
    );
    const totalGains = totalCurrentValue - totalInvestment;
    const overallReturn =
      totalInvestment > 0 ? (totalGains / totalInvestment) * 100 : 0;

    document.getElementById("portfolioValue").textContent =
      this.formatCurrency(totalCurrentValue);
    document.getElementById("totalGains").textContent =
      this.formatCurrency(totalGains);
    document.getElementById(
      "overallReturn"
    ).textContent = `${overallReturn.toFixed(2)}%`;
  }

  getMonthsCount() {
    if (this.data.income.length === 0) return 1;

    const dates = this.data.income.map((item) => new Date(item.date));
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));

    const monthsDiff =
      (maxDate.getFullYear() - minDate.getFullYear()) * 12 +
      (maxDate.getMonth() - minDate.getMonth()) +
      1;

    return Math.max(1, monthsDiff);
  }

  // Rendering Methods
  renderIncomeList() {
    const filter = document.getElementById("incomeFilter").value;
    const filteredIncome =
      filter === "All"
        ? this.data.income
        : this.data.income.filter((item) => item.category === filter);

    const tbody = document.getElementById("incomeList");
    tbody.innerHTML = "";

    filteredIncome.forEach((item) => {
      const row = document.createElement("tr");
      row.innerHTML = `
                <td>${item.source}</td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td><span class="category-badge category-${item.category
                  .toLowerCase()
                  .replace(" ", "-")}">${item.category}</span></td>
                <td>${this.formatDate(item.date)}</td>
                <td>${item.recurring ? "✅" : "❌"}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="financeManager.editIncome('${
                          item.id
                        }')">Edit</button>
                        <button class="action-btn delete-btn" onclick="financeManager.deleteIncome('${
                          item.id
                        }')">Delete</button>
                    </div>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  renderExpenseList() {
    const filter = document.getElementById("expenseFilter").value;
    const filteredExpenses =
      filter === "All"
        ? this.data.expenses
        : this.data.expenses.filter((item) => item.category === filter);

    const tbody = document.getElementById("expenseList");
    tbody.innerHTML = "";

    filteredExpenses.forEach((item) => {
      const row = document.createElement("tr");
      row.innerHTML = `
                <td>${item.name}</td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td><span class="category-badge category-${item.category.toLowerCase()}">${
        item.category
      }</span></td>
                <td>${this.formatDate(item.date)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="financeManager.editExpense('${
                          item.id
                        }')">Edit</button>
                        <button class="action-btn delete-btn" onclick="financeManager.deleteExpense('${
                          item.id
                        }')">Delete</button>
                    </div>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  renderInvestmentList() {
    const filter = document.getElementById("investmentFilter").value;
    const filteredInvestments =
      filter === "All"
        ? this.data.investments
        : this.data.investments.filter((item) => item.type === filter);

    const tbody = document.getElementById("investmentList");
    tbody.innerHTML = "";

    filteredInvestments.forEach((item) => {
      const gainLoss = item.currentValue - item.amount;
      const returnPercent =
        item.amount > 0 ? (gainLoss / item.amount) * 100 : 0;
      const gainLossClass = gainLoss >= 0 ? "positive" : "negative";

      const row = document.createElement("tr");
      row.innerHTML = `
                <td>${item.name}</td>
                <td><span class="category-badge category-${item.type
                  .toLowerCase()
                  .replace(" ", "-")}">${item.type}</span></td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td>${this.formatCurrency(item.currentValue)}</td>
                <td class="metric-change ${gainLossClass}">${this.formatCurrency(
        gainLoss
      )}</td>
                <td class="metric-change ${gainLossClass}">${returnPercent.toFixed(
        2
      )}%</td>
                <td>${this.formatDate(item.date)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="financeManager.editInvestment('${
                          item.id
                        }')">Edit</button>
                        <button class="action-btn delete-btn" onclick="financeManager.deleteInvestment('${
                          item.id
                        }')">Delete</button>
                    </div>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  // PDF Processing Methods
  handleFileSelect(e) {
    const file = e.target.files[0];
    if (file && file.type === "application/pdf") {
      this.selectedFile = file;
      this.showFileSelected(file);
      document.getElementById("processPdfBtn").disabled = false;
    } else {
      this.showToast("Please select a valid PDF file", "error");
    }
  }

  handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById("fileUploadArea").classList.add("dragover");
  }

  handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById("fileUploadArea").classList.remove("dragover");

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type === "application/pdf") {
        this.selectedFile = file;
        this.showFileSelected(file);
        document.getElementById("processPdfBtn").disabled = false;
      } else {
        this.showToast("Please drop a valid PDF file", "error");
      }
    }
  }

  handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById("fileUploadArea").classList.remove("dragover");
  }

  showFileSelected(file) {
    document.getElementById("uploadPlaceholder").style.display = "none";
    document.getElementById("fileSelected").style.display = "flex";
    document.getElementById("fileName").textContent = file.name;
    document.getElementById("fileSize").textContent = this.formatFileSize(
      file.size
    );
    document.getElementById("passwordSection").style.display = "block";
  }

  changeFile() {
    document.getElementById("uploadPlaceholder").style.display = "flex";
    document.getElementById("fileSelected").style.display = "none";
    document.getElementById("passwordSection").style.display = "none";
    document.getElementById("pdfFileInput").value = "";
    document.getElementById("processPdfBtn").disabled = true;
    this.selectedFile = null;
  }

  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  async processPDF() {
    if (!this.selectedFile) {
      this.showToast("Please select a PDF file first", "error");
      return;
    }

    const processPdfBtn = document.getElementById("processPdfBtn");
    const btnText = processPdfBtn.querySelector(".btn-text");
    const btnLoader = processPdfBtn.querySelector(".btn-loader");
    const statusDiv = document.getElementById("processingStatus");

    // Show loading state
    processPdfBtn.disabled = true;
    btnText.style.display = "none";
    btnLoader.style.display = "inline";
    statusDiv.textContent = "Processing PDF...";
    statusDiv.className = "processing-status info";

    try {
      const arrayBuffer = await this.selectedFile.arrayBuffer();
      const password = document.getElementById("pdfPassword").value;

      // Load PDF document
      const loadingTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        password: password || undefined,
      });

      this.pdfDocument = await loadingTask.promise;
      statusDiv.textContent = `PDF loaded successfully. ${this.pdfDocument.numPages} pages found.`;

      // Extract text from all pages
      const extractedData = await this.extractTransactionsFromPDF();

      if (extractedData.length > 0) {
        this.extractedTransactions = extractedData;
        this.showPreviewSection();
        this.renderExtractedTransactions();
        statusDiv.textContent = `Successfully extracted ${extractedData.length} transactions.`;
        statusDiv.className = "processing-status success";
      } else {
        statusDiv.textContent =
          "No transactions found in the PDF. Please check the format.";
        statusDiv.className = "processing-status error";
      }
    } catch (error) {
      console.error("PDF processing error:", error);
      if (error.name === "PasswordException") {
        statusDiv.textContent =
          "Invalid password. Please enter the correct password.";
      } else {
        statusDiv.textContent =
          "Error processing PDF. Please check the file format.";
      }
      statusDiv.className = "processing-status error";
    } finally {
      // Reset button state
      processPdfBtn.disabled = false;
      btnText.style.display = "inline";
      btnLoader.style.display = "none";
    }
  }

  async extractTransactionsFromPDF() {
    const transactions = [];

    try {
      for (let pageNum = 1; pageNum <= this.pdfDocument.numPages; pageNum++) {
        const page = await this.pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Extract text items
        const textItems = textContent.items.map((item) => item.str);
        const pageTransactions = this.parseTransactionsFromText(textItems);
        transactions.push(...pageTransactions);
      }
    } catch (error) {
      console.error("Error extracting text from PDF:", error);
    }

    return transactions;
  }

  parseTransactionsFromText(textItems) {
    const transactions = [];
    const dateRegex =
      /(\d{1,2}\/\d{1,2}\/\d{2,4}|\d{1,2}-\d{1,2}-\d{2,4}|\d{2}\.\d{2}\.\d{4})/;
    const amountRegex = /₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    // Look for table-like structures
    for (let i = 0; i < textItems.length - 2; i++) {
      const currentItem = textItems[i];
      const nextItem = textItems[i + 1];
      const thirdItem = textItems[i + 2];

      // Check if current item looks like a date
      if (dateRegex.test(currentItem)) {
        let description = "";
        let amount = 0;

        // Look for description in next few items
        for (let j = i + 1; j < Math.min(i + 5, textItems.length); j++) {
          const item = textItems[j];

          // If we find an amount, stop looking for description
          const amountMatch = item.match(amountRegex);
          if (amountMatch) {
            amount = parseFloat(amountMatch[1].replace(/,/g, ""));
            break;
          } else if (!dateRegex.test(item) && item.trim().length > 2) {
            // Add to description if it's not a date and has meaningful content
            description += (description ? " " : "") + item.trim();
          }
        }

        // Only add if we found both description and amount
        if (description && amount > 0) {
          transactions.push({
            id: this.generateId(),
            date: this.parseDate(currentItem),
            description: description.substring(0, 100), // Limit description length
            amount: amount,
            category: this.suggestCategory(description),
            selected: true,
          });
        }
      }
    }

    return transactions;
  }

  parseDate(dateString) {
    // Handle various date formats
    const formats = [
      /(\d{1,2})\/(\d{1,2})\/(\d{2,4})/, // MM/DD/YYYY or DD/MM/YYYY
      /(\d{1,2})-(\d{1,2})-(\d{2,4})/, // MM-DD-YYYY or DD-MM-YYYY
      /(\d{2})\.(\d{2})\.(\d{4})/, // DD.MM.YYYY
    ];

    for (const format of formats) {
      const match = dateString.match(format);
      if (match) {
        let [, part1, part2, part3] = match;

        // Assume DD/MM/YYYY format for Indian context
        const day = parseInt(part1);
        const month = parseInt(part2) - 1; // JavaScript months are 0-indexed
        const year =
          part3.length === 2 ? 2000 + parseInt(part3) : parseInt(part3);

        const date = new Date(year, month, day);
        return date.toISOString().split("T")[0];
      }
    }

    // Fallback to current date
    return new Date().toISOString().split("T")[0];
  }

  suggestCategory(description) {
    const categoryKeywords = {
      Food: [
        "restaurant",
        "cafe",
        "food",
        "dining",
        "pizza",
        "burger",
        "swiggy",
        "zomato",
        "dominos",
      ],
      Transport: [
        "uber",
        "ola",
        "taxi",
        "metro",
        "bus",
        "petrol",
        "fuel",
        "parking",
      ],
      Shopping: [
        "amazon",
        "flipkart",
        "mall",
        "store",
        "shop",
        "purchase",
        "buy",
      ],
      Bills: [
        "electricity",
        "water",
        "gas",
        "internet",
        "mobile",
        "phone",
        "utility",
      ],
      Entertainment: [
        "movie",
        "cinema",
        "netflix",
        "spotify",
        "game",
        "entertainment",
      ],
      Healthcare: [
        "hospital",
        "doctor",
        "medical",
        "pharmacy",
        "health",
        "clinic",
      ],
      Travel: ["hotel", "flight", "train", "booking", "travel", "trip"],
      Education: [
        "school",
        "college",
        "course",
        "book",
        "education",
        "tuition",
      ],
    };

    const lowerDescription = description.toLowerCase();

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some((keyword) => lowerDescription.includes(keyword))) {
        return category;
      }
    }

    return "Other";
  }

  showPreviewSection() {
    document.getElementById("previewSection").style.display = "block";
  }

  renderExtractedTransactions() {
    const tbody = document.getElementById("extractedTransactions");
    tbody.innerHTML = "";

    this.extractedTransactions.forEach((transaction, index) => {
      const row = document.createElement("tr");
      row.innerHTML = `
                <td>
                    <input type="checkbox" ${
                      transaction.selected ? "checked" : ""
                    }
                           onchange="financeManager.toggleTransactionSelection(${index})">
                </td>
                <td>${this.formatDate(transaction.date)}</td>
                <td>${transaction.description}</td>
                <td>${this.formatCurrency(transaction.amount)}</td>
                <td>
                    <select class="category-select" onchange="financeManager.updateTransactionCategory(${index}, this.value)">
                        <option value="Food" ${
                          transaction.category === "Food" ? "selected" : ""
                        }>Food</option>
                        <option value="Transport" ${
                          transaction.category === "Transport" ? "selected" : ""
                        }>Transport</option>
                        <option value="Entertainment" ${
                          transaction.category === "Entertainment"
                            ? "selected"
                            : ""
                        }>Entertainment</option>
                        <option value="Shopping" ${
                          transaction.category === "Shopping" ? "selected" : ""
                        }>Shopping</option>
                        <option value="Bills" ${
                          transaction.category === "Bills" ? "selected" : ""
                        }>Bills</option>
                        <option value="Healthcare" ${
                          transaction.category === "Healthcare"
                            ? "selected"
                            : ""
                        }>Healthcare</option>
                        <option value="Education" ${
                          transaction.category === "Education" ? "selected" : ""
                        }>Education</option>
                        <option value="Travel" ${
                          transaction.category === "Travel" ? "selected" : ""
                        }>Travel</option>
                        <option value="Other" ${
                          transaction.category === "Other" ? "selected" : ""
                        }>Other</option>
                    </select>
                </td>
                <td>
                    <button class="action-btn delete-btn" onclick="financeManager.removeExtractedTransaction(${index})">Remove</button>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  toggleTransactionSelection(index) {
    this.extractedTransactions[index].selected =
      !this.extractedTransactions[index].selected;
  }

  updateTransactionCategory(index, category) {
    this.extractedTransactions[index].category = category;
  }

  removeExtractedTransaction(index) {
    this.extractedTransactions.splice(index, 1);
    this.renderExtractedTransactions();
  }

  selectAllTransactions() {
    this.extractedTransactions.forEach(
      (transaction) => (transaction.selected = true)
    );
    this.renderExtractedTransactions();
  }

  deselectAllTransactions() {
    this.extractedTransactions.forEach(
      (transaction) => (transaction.selected = false)
    );
    this.renderExtractedTransactions();
  }

  importSelectedTransactions() {
    const selectedTransactions = this.extractedTransactions.filter(
      (t) => t.selected
    );

    if (selectedTransactions.length === 0) {
      this.showToast(
        "Please select at least one transaction to import",
        "warning"
      );
      return;
    }

    // Convert to expense format and add to expenses
    selectedTransactions.forEach((transaction) => {
      const expense = {
        id: this.generateId(),
        name: transaction.description,
        amount: transaction.amount,
        category: transaction.category,
        date: transaction.date,
        timestamp: new Date().toISOString(),
        source: "Credit Card Import",
      };
      this.data.expenses.push(expense);
    });

    // Add to import history
    const importRecord = {
      id: this.generateId(),
      filename: this.selectedFile.name,
      date: new Date().toISOString(),
      transactionsCount: selectedTransactions.length,
      totalAmount: selectedTransactions.reduce((sum, t) => sum + t.amount, 0),
    };
    this.data.importHistory.push(importRecord);

    this.saveData();
    this.updateAllMetrics();
    this.showToast(
      `Successfully imported ${selectedTransactions.length} transactions!`,
      "success"
    );

    // Reset the import interface
    this.resetImportInterface();
  }

  resetImportInterface() {
    document.getElementById("previewSection").style.display = "none";
    this.changeFile();
    this.extractedTransactions = [];
    document.getElementById("processingStatus").textContent = "";
    this.renderImportHistory();
  }

  renderImportHistory() {
    const historyList = document.getElementById("importHistory");
    historyList.innerHTML = "";

    if (this.data.importHistory.length === 0) {
      historyList.innerHTML =
        '<p style="text-align: center; color: var(--gray-500); padding: 2rem;">No import history yet</p>';
      return;
    }

    this.data.importHistory.reverse().forEach((record) => {
      const historyItem = document.createElement("div");
      historyItem.className = "history-item";
      historyItem.innerHTML = `
                <div class="history-info">
                    <div class="history-filename">${record.filename}</div>
                    <div class="history-date">${this.formatDate(
                      record.date
                    )}</div>
                </div>
                <div class="history-stats">
                    <span>${record.transactionsCount} transactions</span>
                    <span>${this.formatCurrency(record.totalAmount)}</span>
                </div>
            `;
      historyList.appendChild(historyItem);
    });
  }

  // Budget Methods
  openBudgetModal() {
    document.getElementById("budgetModal").classList.add("active");
    if (this.data.budget.monthly) {
      document.getElementById("budgetAmount").value = this.data.budget.monthly;
    }
  }

  closeBudgetModal() {
    document.getElementById("budgetModal").classList.remove("active");
  }

  updateBudgetDisplay() {
    const monthlyBudget = this.data.budget.monthly || 0;
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyExpenses = this.data.expenses
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const budgetUsed =
      monthlyBudget > 0 ? (monthlyExpenses / monthlyBudget) * 100 : 0;

    document.getElementById("monthlyBudget").textContent =
      this.formatCurrency(monthlyBudget);
    document.getElementById("budgetProgress").style.width = `${Math.min(
      budgetUsed,
      100
    )}%`;
    document.getElementById(
      "budgetProgressText"
    ).textContent = `${budgetUsed.toFixed(1)}% used`;

    // Update progress bar color based on usage
    const progressBar = document.getElementById("budgetProgress");
    if (budgetUsed > 90) {
      progressBar.style.background = "var(--danger-color)";
    } else if (budgetUsed > 75) {
      progressBar.style.background = "var(--warning-color)";
    } else {
      progressBar.style.background =
        "linear-gradient(90deg, var(--success-color), var(--warning-color))";
    }
  }

  renderCategoryBudgets() {
    const categoryBudgets = document.getElementById("categoryBudgets");
    const categories = [
      "Food",
      "Transport",
      "Entertainment",
      "Shopping",
      "Bills",
      "Healthcare",
      "Education",
      "Travel",
      "Other",
    ];

    categoryBudgets.innerHTML = "";

    categories.forEach((category) => {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();

      const categoryExpenses = this.data.expenses
        .filter((item) => {
          const itemDate = new Date(item.date);
          return (
            item.category === category &&
            itemDate.getMonth() === currentMonth &&
            itemDate.getFullYear() === currentYear
          );
        })
        .reduce((sum, item) => sum + item.amount, 0);

      const budgetCard = document.createElement("div");
      budgetCard.className = "budget-category-card";
      budgetCard.innerHTML = `
                <div class="budget-category-header">
                    <span class="budget-category-name">${category}</span>
                    <span class="budget-category-amount">${this.formatCurrency(
                      categoryExpenses
                    )}</span>
                </div>
                <div class="budget-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${Math.min(
                          (categoryExpenses / 10000) * 100,
                          100
                        )}%"></div>
                    </div>
                </div>
            `;
      categoryBudgets.appendChild(budgetCard);
    });
  }

  // Chart Methods (simplified for now)
  renderCharts() {
    this.renderExpenseChart();
    this.renderIncomeExpenseChart();
    this.renderNetWorthChart();
    this.renderPortfolioChart();
  }

  renderExpenseChart() {
    const ctx = document.getElementById("expenseChart").getContext("2d");

    // Destroy existing chart if it exists
    if (this.charts.expenseChart) {
      this.charts.expenseChart.destroy();
    }

    const categories = [
      "Food",
      "Transport",
      "Entertainment",
      "Shopping",
      "Bills",
      "Healthcare",
      "Education",
      "Travel",
      "Other",
    ];
    const data = categories.map((category) =>
      this.data.expenses
        .filter((expense) => expense.category === category)
        .reduce((sum, expense) => sum + expense.amount, 0)
    );

    this.charts.expenseChart = new Chart(ctx, {
      type: "doughnut",
      data: {
        labels: categories,
        datasets: [
          {
            data: data,
            backgroundColor: [
              "#ef4444",
              "#3b82f6",
              "#8b5cf6",
              "#10b981",
              "#f59e0b",
              "#06b6d4",
              "#84cc16",
              "#f97316",
              "#6b7280",
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  renderIncomeExpenseChart() {
    const ctx = document.getElementById("incomeExpenseChart").getContext("2d");

    if (this.charts.incomeExpenseChart) {
      this.charts.incomeExpenseChart.destroy();
    }

    // Get last 6 months data
    const months = [];
    const incomeData = [];
    const expenseData = [];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const month = date.getMonth();
      const year = date.getFullYear();

      months.push(date.toLocaleDateString("en-IN", { month: "short" }));

      const monthIncome = this.data.income
        .filter((item) => {
          const itemDate = new Date(item.date);
          return (
            itemDate.getMonth() === month && itemDate.getFullYear() === year
          );
        })
        .reduce((sum, item) => sum + item.amount, 0);

      const monthExpense = this.data.expenses
        .filter((item) => {
          const itemDate = new Date(item.date);
          return (
            itemDate.getMonth() === month && itemDate.getFullYear() === year
          );
        })
        .reduce((sum, item) => sum + item.amount, 0);

      incomeData.push(monthIncome);
      expenseData.push(monthExpense);
    }

    this.charts.incomeExpenseChart = new Chart(ctx, {
      type: "bar",
      data: {
        labels: months,
        datasets: [
          {
            label: "Income",
            data: incomeData,
            backgroundColor: "#10b981",
          },
          {
            label: "Expenses",
            data: expenseData,
            backgroundColor: "#ef4444",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  renderNetWorthChart() {
    const ctx = document.getElementById("netWorthChart").getContext("2d");

    if (this.charts.netWorthChart) {
      this.charts.netWorthChart.destroy();
    }

    // Simplified net worth trend (last 6 months)
    const months = [];
    const netWorthData = [];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push(date.toLocaleDateString("en-IN", { month: "short" }));

      // Simplified calculation
      const totalIncome = this.data.income.reduce(
        (sum, item) => sum + item.amount,
        0
      );
      const totalExpenses = this.data.expenses.reduce(
        (sum, item) => sum + item.amount,
        0
      );
      const totalInvestments = this.data.investments.reduce(
        (sum, item) => sum + item.currentValue,
        0
      );

      netWorthData.push(totalIncome - totalExpenses + totalInvestments);
    }

    this.charts.netWorthChart = new Chart(ctx, {
      type: "line",
      data: {
        labels: months,
        datasets: [
          {
            label: "Net Worth",
            data: netWorthData,
            borderColor: "#3b82f6",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            fill: true,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  renderPortfolioChart() {
    const ctx = document.getElementById("portfolioChart").getContext("2d");

    if (this.charts.portfolioChart) {
      this.charts.portfolioChart.destroy();
    }

    const types = [
      "Stocks",
      "Mutual Funds",
      "Fixed Deposits",
      "Crypto",
      "Bonds",
      "Real Estate",
    ];
    const data = types.map((type) =>
      this.data.investments
        .filter((investment) => investment.type === type)
        .reduce((sum, investment) => sum + investment.currentValue, 0)
    );

    this.charts.portfolioChart = new Chart(ctx, {
      type: "pie",
      data: {
        labels: types,
        datasets: [
          {
            data: data,
            backgroundColor: [
              "#ef4444",
              "#3b82f6",
              "#10b981",
              "#f59e0b",
              "#8b5cf6",
              "#06b6d4",
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  renderRecentTransactions() {
    const recentTransactions = document.getElementById("recentTransactions");

    // Combine all transactions and sort by date
    const allTransactions = [
      ...this.data.income.map((item) => ({ ...item, type: "income" })),
      ...this.data.expenses.map((item) => ({ ...item, type: "expense" })),
    ]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    recentTransactions.innerHTML = "";

    if (allTransactions.length === 0) {
      recentTransactions.innerHTML =
        '<p style="text-align: center; color: var(--gray-500); padding: 2rem;">No transactions yet</p>';
      return;
    }

    allTransactions.forEach((transaction) => {
      const transactionItem = document.createElement("div");
      transactionItem.className = "transaction-item";

      const name =
        transaction.type === "income" ? transaction.source : transaction.name;
      const amountClass = transaction.type === "income" ? "income" : "expense";
      const amountPrefix = transaction.type === "income" ? "+" : "-";

      transactionItem.innerHTML = `
                <div class="transaction-info">
                    <div class="transaction-name">${name}</div>
                    <div class="transaction-category">${
                      transaction.category
                    } • ${this.formatDate(transaction.date)}</div>
                </div>
                <div class="transaction-amount ${amountClass}">${amountPrefix}${this.formatCurrency(
        transaction.amount
      )}</div>
            `;
      recentTransactions.appendChild(transactionItem);
    });
  }

  // Edit methods (simplified for now)
  editIncome(id) {
    this.showToast("Edit functionality coming soon!", "info");
  }

  editExpense(id) {
    this.showToast("Edit functionality coming soon!", "info");
  }

  editInvestment(id) {
    this.showToast("Edit functionality coming soon!", "info");
  }
}

// Initialize the application
const financeManager = new FinanceManager();

// Initialize the application
