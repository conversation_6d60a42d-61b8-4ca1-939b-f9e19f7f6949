// Personal Finance Manager - Comprehensive Application with Credit Card Import
// Initialize PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc =
  "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js";

class FinanceManager {
  constructor() {
    this.data = {
      income: JSON.parse(localStorage.getItem("finance_income") || "[]"),
      expenses: JSON.parse(localStorage.getItem("finance_expenses") || "[]"),
      investments: JSON.parse(
        localStorage.getItem("finance_investments") || "[]"
      ),
      budget: JSON.parse(localStorage.getItem("finance_budget") || "{}"),
      importHistory: JSON.parse(
        localStorage.getItem("finance_import_history") || "[]"
      ),
    };

    this.currentPage = "overview";
    this.charts = {};
    this.extractedTransactions = [];
    this.categoryLearning = {}; // For dynamic category learning during import
    this.editingExpenseId = null; // Track expense being edited
    this.isEditMode = false; // Track if form is in edit mode

    this.init();
  }

  init() {
    // Migrate existing expenses to include selection state
    this.migrateExpenseData();
    this.setupEventListeners();
    this.setupSidebar();
    this.setupPDFProcessor();
    this.loadPage("overview");
    this.updateAllMetrics();
    this.setDefaultDates();
  }

  migrateExpenseData() {
    // Add selection state to existing expenses that don't have it
    let migrated = false;
    this.data.expenses.forEach((expense) => {
      if (expense.selected === undefined) {
        expense.selected = false;
        migrated = true;
      }
    });

    if (migrated) {
      this.saveData();
    }
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        this.loadPage(page);
      });
    });

    // Forms
    document
      .getElementById("incomeForm")
      .addEventListener("submit", (e) => this.handleIncomeSubmit(e));
    document
      .getElementById("expenseForm")
      .addEventListener("submit", (e) => this.handleExpenseSubmit(e));
    document
      .getElementById("investmentForm")
      .addEventListener("submit", (e) => this.handleInvestmentSubmit(e));
    document
      .getElementById("budgetForm")
      .addEventListener("submit", (e) => this.handleBudgetSubmit(e));

    // Filters
    document
      .getElementById("incomeFilter")
      .addEventListener("change", () => this.renderIncomeList());
    document
      .getElementById("expenseFilter")
      .addEventListener("change", () => this.renderExpenseList());
    document
      .getElementById("investmentFilter")
      .addEventListener("change", () => this.renderInvestmentList());

    // Budget modal
    document
      .getElementById("setBudgetBtn")
      .addEventListener("click", () => this.openBudgetModal());
    document
      .getElementById("closeBudgetModal")
      .addEventListener("click", () => this.closeBudgetModal());

    // Cancel edit expense
    document
      .getElementById("cancelEditExpenseBtn")
      .addEventListener("click", () => this.cancelEditExpense());

    // PDF Import
    document
      .getElementById("pdfFileInput")
      .addEventListener("change", (e) => this.handleFileSelect(e));
    document
      .getElementById("processPdfBtn")
      .addEventListener("click", () => this.processPDF());
    document
      .getElementById("importAllBtn")
      .addEventListener("click", () => this.importSelectedTransactions());
    document
      .getElementById("selectAllBtn")
      .addEventListener("click", () => this.selectAllTransactions());
    document
      .getElementById("deselectAllBtn")
      .addEventListener("click", () => this.deselectAllTransactions());
    document
      .getElementById("changeFileBtn")
      .addEventListener("click", () => this.changeFile());

    // File upload area
    const uploadArea = document.getElementById("fileUploadArea");
    uploadArea.addEventListener("click", () =>
      document.getElementById("pdfFileInput").click()
    );
    uploadArea.addEventListener("dragover", (e) => this.handleDragOver(e));
    uploadArea.addEventListener("drop", (e) => this.handleDrop(e));
    uploadArea.addEventListener("dragleave", (e) => this.handleDragLeave(e));
  }

  setupSidebar() {
    const sidebar = document.getElementById("sidebar");
    const sidebarToggle = document.getElementById("sidebarToggle");

    sidebarToggle.addEventListener("click", () => {
      sidebar.classList.toggle("collapsed");
    });

    // Mobile responsiveness
    if (window.innerWidth <= 1024) {
      sidebar.classList.add("collapsed");
    }

    window.addEventListener("resize", () => {
      if (window.innerWidth <= 1024) {
        sidebar.classList.add("mobile-open");
      } else {
        sidebar.classList.remove("mobile-open");
      }
    });
  }

  setupPDFProcessor() {
    // Initialize PDF processing variables
    this.selectedFile = null;
    this.pdfDocument = null;
  }

  setDefaultDates() {
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("incomeDate").value = today;
    document.getElementById("expenseDate").value = today;
    document.getElementById("investmentDate").value = today;
  }

  loadPage(pageName) {
    // Hide all pages
    document.querySelectorAll(".page").forEach((page) => {
      page.classList.remove("active");
    });

    // Show selected page
    document.getElementById(`${pageName}-page`).classList.add("active");

    // Update navigation
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.classList.remove("active");
    });
    document.querySelector(`[data-page="${pageName}"]`).classList.add("active");

    this.currentPage = pageName;

    // Load page-specific content
    switch (pageName) {
      case "overview":
        this.loadOverviewPage();
        break;
      case "income":
        this.loadIncomePage();
        break;
      case "expenses":
        this.loadExpensePage();
        break;
      case "investments":
        this.loadInvestmentPage();
        break;
      case "budget":
        this.loadBudgetPage();
        break;
      case "credit-import":
        this.loadCreditImportPage();
        break;
    }
  }

  // Data Management Methods
  saveData() {
    localStorage.setItem("finance_income", JSON.stringify(this.data.income));
    localStorage.setItem(
      "finance_expenses",
      JSON.stringify(this.data.expenses)
    );
    localStorage.setItem(
      "finance_investments",
      JSON.stringify(this.data.investments)
    );
    localStorage.setItem("finance_budget", JSON.stringify(this.data.budget));
    localStorage.setItem(
      "finance_import_history",
      JSON.stringify(this.data.importHistory)
    );
  }

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  formatCurrency(amount) {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }

  showToast(message, type = "info") {
    const toast = document.getElementById("toast");
    const toastIcon = document.getElementById("toastIcon");
    const toastMessage = document.getElementById("toastMessage");

    const icons = {
      success: "✅",
      error: "❌",
      info: "ℹ️",
      warning: "⚠️",
    };

    toast.className = `toast ${type}`;
    toastIcon.textContent = icons[type] || icons.info;
    toastMessage.textContent = message;

    toast.classList.add("show");

    setTimeout(() => {
      toast.classList.remove("show");
    }, 3000);
  }

  // Form Handlers
  handleIncomeSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const income = {
      id: this.generateId(),
      source: document.getElementById("incomeSource").value,
      amount: parseFloat(document.getElementById("incomeAmount").value),
      category: document.getElementById("incomeCategory").value,
      date: document.getElementById("incomeDate").value,
      recurring: document.getElementById("recurringIncome").checked,
      timestamp: new Date().toISOString(),
    };

    this.data.income.push(income);
    this.saveData();
    this.renderIncomeList();
    this.updateAllMetrics();
    this.showToast("Income added successfully!", "success");
    e.target.reset();
    this.setDefaultDates();
  }

  handleExpenseSubmit(e) {
    e.preventDefault();

    const expenseData = {
      name: document.getElementById("expenseName").value,
      amount: parseFloat(document.getElementById("expenseAmount").value),
      category: document.getElementById("expenseCategory").value,
      date: document.getElementById("expenseDate").value,
    };

    // Validate form data
    if (
      !expenseData.name ||
      !expenseData.amount ||
      !expenseData.category ||
      !expenseData.date
    ) {
      this.showToast("Please fill in all required fields!", "error");
      return;
    }

    if (expenseData.amount <= 0) {
      this.showToast("Amount must be greater than 0!", "error");
      return;
    }

    if (this.isEditMode && this.editingExpenseId) {
      // Update existing expense
      const expenseIndex = this.data.expenses.findIndex(
        (item) => item.id === this.editingExpenseId
      );

      if (expenseIndex !== -1) {
        // Preserve original properties and update with new data
        this.data.expenses[expenseIndex] = {
          ...this.data.expenses[expenseIndex],
          ...expenseData,
          timestamp: new Date().toISOString(), // Update timestamp to track when it was edited
        };

        this.saveData();
        this.renderExpenseList();
        this.updateAllMetrics();
        this.resetExpenseForm();
        this.showToast("Expense updated successfully!", "success");
      } else {
        this.showToast("Error updating expense!", "error");
      }
    } else {
      // Add new expense
      const expense = {
        id: this.generateId(),
        ...expenseData,
        timestamp: new Date().toISOString(),
        selected: false, // Add selection state for bulk operations
      };

      this.data.expenses.push(expense);
      this.saveData();
      this.renderExpenseList();
      this.updateAllMetrics();
      this.showToast("Expense added successfully!", "success");
      e.target.reset();
      this.setDefaultDates();
    }
  }

  handleInvestmentSubmit(e) {
    e.preventDefault();

    const investment = {
      id: this.generateId(),
      name: document.getElementById("investmentName").value,
      type: document.getElementById("investmentType").value,
      amount: parseFloat(document.getElementById("investmentAmount").value),
      currentValue: parseFloat(document.getElementById("currentValue").value),
      date: document.getElementById("investmentDate").value,
      timestamp: new Date().toISOString(),
    };

    this.data.investments.push(investment);
    this.saveData();
    this.renderInvestmentList();
    this.updateAllMetrics();
    this.showToast("Investment added successfully!", "success");
    e.target.reset();
    this.setDefaultDates();
  }

  handleBudgetSubmit(e) {
    e.preventDefault();

    const budgetAmount = parseFloat(
      document.getElementById("budgetAmount").value
    );
    this.data.budget.monthly = budgetAmount;
    this.data.budget.setDate = new Date().toISOString();

    this.saveData();
    this.closeBudgetModal();
    this.updateBudgetDisplay();
    this.showToast("Budget set successfully!", "success");
  }

  // Delete Methods
  deleteIncome(id) {
    if (confirm("Are you sure you want to delete this income entry?")) {
      this.data.income = this.data.income.filter((item) => item.id !== id);
      this.saveData();
      this.renderIncomeList();
      this.updateAllMetrics();
      this.showToast("Income deleted successfully!", "success");
    }
  }

  deleteExpense(id) {
    if (confirm("Are you sure you want to delete this expense?")) {
      this.data.expenses = this.data.expenses.filter((item) => item.id !== id);
      this.saveData();
      this.renderExpenseList();
      this.updateAllMetrics();
      this.showToast("Expense deleted successfully!", "success");
    }
  }

  deleteInvestment(id) {
    if (confirm("Are you sure you want to delete this investment?")) {
      this.data.investments = this.data.investments.filter(
        (item) => item.id !== id
      );
      this.saveData();
      this.renderInvestmentList();
      this.updateAllMetrics();
      this.showToast("Investment deleted successfully!", "success");
    }
  }

  // Page Loading Methods
  loadOverviewPage() {
    this.updateAllMetrics();
    this.renderCharts();
    this.renderRecentTransactions();
  }

  loadIncomePage() {
    this.updateIncomeAnalytics();
    this.renderIncomeList();
  }

  loadExpensePage() {
    this.updateExpenseAnalytics();
    this.renderExpenseList();
  }

  loadInvestmentPage() {
    this.updateInvestmentAnalytics();
    this.renderInvestmentList();
  }

  loadBudgetPage() {
    this.updateBudgetDisplay();
    this.renderCategoryBudgets();
  }

  loadCreditImportPage() {
    this.renderImportHistory();
  }

  // Metrics and Analytics
  updateAllMetrics() {
    const totalIncome = this.data.income.reduce(
      (sum, item) => sum + item.amount,
      0
    );
    const totalExpenses = this.data.expenses.reduce(
      (sum, item) => sum + item.amount,
      0
    );
    const totalInvestments = this.data.investments.reduce(
      (sum, item) => sum + item.currentValue,
      0
    );
    const netWorth = totalIncome - totalExpenses + totalInvestments;

    // Current month calculations
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyIncome = this.data.income
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const monthlyExpenses = this.data.expenses
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    // Update UI
    document.getElementById("netWorth").textContent =
      this.formatCurrency(netWorth);
    document.getElementById("monthlyIncome").textContent =
      this.formatCurrency(monthlyIncome);
    document.getElementById("monthlyExpenses").textContent =
      this.formatCurrency(monthlyExpenses);
    document.getElementById("totalInvestments").textContent =
      this.formatCurrency(totalInvestments);

    // Update change indicators (simplified for now)
    document.getElementById("netWorthChange").textContent = "+0.00%";
    document.getElementById("incomeChange").textContent = "+0.00%";
    document.getElementById("expenseChange").textContent = "+0.00%";
    document.getElementById("investmentChange").textContent = "+0.00%";
  }

  updateIncomeAnalytics() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    const currentMonthIncome = this.data.income
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const lastMonthIncome = this.data.income
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === lastMonth &&
          itemDate.getFullYear() === lastMonthYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const avgMonthlyIncome =
      this.data.income.length > 0
        ? this.data.income.reduce((sum, item) => sum + item.amount, 0) /
          Math.max(1, this.getMonthsCount())
        : 0;

    document.getElementById("currentMonthIncome").textContent =
      this.formatCurrency(currentMonthIncome);
    document.getElementById("lastMonthIncome").textContent =
      this.formatCurrency(lastMonthIncome);
    document.getElementById("avgMonthlyIncome").textContent =
      this.formatCurrency(avgMonthlyIncome);
  }

  updateExpenseAnalytics() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const currentMonthExpenses = this.data.expenses
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const monthlyBudget = this.data.budget.monthly || 0;
    const budgetRemaining = monthlyBudget - currentMonthExpenses;
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const dailyAverage = currentMonthExpenses / new Date().getDate();

    document.getElementById("currentMonthExpenses").textContent =
      this.formatCurrency(currentMonthExpenses);
    document.getElementById("budgetRemaining").textContent =
      this.formatCurrency(budgetRemaining);
    document.getElementById("dailyAverage").textContent =
      this.formatCurrency(dailyAverage);
  }

  updateInvestmentAnalytics() {
    const totalInvestment = this.data.investments.reduce(
      (sum, item) => sum + item.amount,
      0
    );
    const totalCurrentValue = this.data.investments.reduce(
      (sum, item) => sum + item.currentValue,
      0
    );
    const totalGains = totalCurrentValue - totalInvestment;
    const overallReturn =
      totalInvestment > 0 ? (totalGains / totalInvestment) * 100 : 0;

    document.getElementById("portfolioValue").textContent =
      this.formatCurrency(totalCurrentValue);
    document.getElementById("totalGains").textContent =
      this.formatCurrency(totalGains);
    document.getElementById(
      "overallReturn"
    ).textContent = `${overallReturn.toFixed(2)}%`;
  }

  getMonthsCount() {
    if (this.data.income.length === 0) return 1;

    const dates = this.data.income.map((item) => new Date(item.date));
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));

    const monthsDiff =
      (maxDate.getFullYear() - minDate.getFullYear()) * 12 +
      (maxDate.getMonth() - minDate.getMonth()) +
      1;

    return Math.max(1, monthsDiff);
  }

  // Rendering Methods
  renderIncomeList() {
    const filter = document.getElementById("incomeFilter").value;
    const filteredIncome =
      filter === "All"
        ? this.data.income
        : this.data.income.filter((item) => item.category === filter);

    const tbody = document.getElementById("incomeList");
    tbody.innerHTML = "";

    filteredIncome.forEach((item) => {
      const row = document.createElement("tr");
      row.innerHTML = `
                <td>${item.source}</td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td><span class="category-badge category-${item.category
                  .toLowerCase()
                  .replace(" ", "-")}">${item.category}</span></td>
                <td>${this.formatDate(item.date)}</td>
                <td>${item.recurring ? "✅" : "❌"}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="financeManager.editIncome('${
                          item.id
                        }')">Edit</button>
                        <button class="action-btn delete-btn" onclick="financeManager.deleteIncome('${
                          item.id
                        }')">Delete</button>
                    </div>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  renderExpenseList() {
    const filter = document.getElementById("expenseFilter").value;
    const filteredExpenses =
      filter === "All"
        ? this.data.expenses
        : this.data.expenses.filter((item) => item.category === filter);

    const tbody = document.getElementById("expenseList");
    tbody.innerHTML = "";

    filteredExpenses.forEach((item) => {
      const row = document.createElement("tr");
      row.innerHTML = `
                <td>
                    <input type="checkbox" ${
                      item.selected ? "checked" : ""
                    } onchange="financeManager.toggleExpenseSelection('${
        item.id
      }')">
                </td>
                <td>${item.name}</td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td><span class="category-badge category-${item.category.toLowerCase()}">${
        item.category
      }</span></td>
                <td>${this.formatDate(item.date)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="financeManager.editExpense('${
                          item.id
                        }')">Edit</button>
                        <button class="action-btn delete-btn" onclick="financeManager.deleteExpense('${
                          item.id
                        }')">Delete</button>
                    </div>
                </td>
            `;
      tbody.appendChild(row);
    });

    // Update filtered total
    this.updateExpenseFilteredTotal(filteredExpenses, filter);

    // Update delete selected button visibility
    this.updateDeleteSelectedButtonVisibility();
  }

  updateExpenseFilteredTotal(filteredExpenses, filter) {
    const total = filteredExpenses.reduce(
      (sum, expense) => sum + expense.amount,
      0
    );
    const labelElement = document.getElementById("expenseFilteredLabel");
    const totalElement = document.getElementById("expenseFilteredTotal");

    if (filter === "All") {
      labelElement.textContent = "Total:";
    } else {
      labelElement.textContent = `Total for ${filter}:`;
    }

    totalElement.textContent = this.formatCurrency(total);
  }

  // Bulk selection methods for expenses
  selectAllExpenses() {
    this.data.expenses.forEach((expense) => {
      expense.selected = true;
    });
    this.renderExpenseList();
    this.showToast("All expenses selected", "info");
  }

  deselectAllExpenses() {
    this.data.expenses.forEach((expense) => {
      expense.selected = false;
    });
    this.renderExpenseList();
    this.showToast("All expenses deselected", "info");
  }

  toggleExpenseSelection(id) {
    const expense = this.data.expenses.find((item) => item.id === id);
    if (expense) {
      expense.selected = !expense.selected;
      this.updateDeleteSelectedButtonVisibility();
    }
  }

  updateDeleteSelectedButtonVisibility() {
    const selectedCount = this.data.expenses.filter(
      (expense) => expense.selected
    ).length;
    const deleteBtn = document.getElementById("deleteSelectedBtn");

    if (selectedCount > 0) {
      deleteBtn.style.display = "inline-block";
      deleteBtn.textContent = `Delete Selected (${selectedCount})`;
    } else {
      deleteBtn.style.display = "none";
    }
  }

  deleteSelectedExpenses() {
    const selectedExpenses = this.data.expenses.filter(
      (expense) => expense.selected
    );
    const count = selectedExpenses.length;

    if (count === 0) {
      this.showToast("No expenses selected", "warning");
      return;
    }

    const confirmMessage = `Are you sure you want to delete ${count} selected expense${
      count > 1 ? "s" : ""
    }?`;

    if (confirm(confirmMessage)) {
      // Remove selected expenses
      this.data.expenses = this.data.expenses.filter(
        (expense) => !expense.selected
      );

      // Save data and update UI
      this.saveData();
      this.renderExpenseList();
      this.updateAllMetrics();

      this.showToast(
        `Successfully deleted ${count} expense${count > 1 ? "s" : ""}!`,
        "success"
      );
    }
  }

  renderInvestmentList() {
    const filter = document.getElementById("investmentFilter").value;
    const filteredInvestments =
      filter === "All"
        ? this.data.investments
        : this.data.investments.filter((item) => item.type === filter);

    const tbody = document.getElementById("investmentList");
    tbody.innerHTML = "";

    filteredInvestments.forEach((item) => {
      const gainLoss = item.currentValue - item.amount;
      const returnPercent =
        item.amount > 0 ? (gainLoss / item.amount) * 100 : 0;
      const gainLossClass = gainLoss >= 0 ? "positive" : "negative";

      const row = document.createElement("tr");
      row.innerHTML = `
                <td>${item.name}</td>
                <td><span class="category-badge category-${item.type
                  .toLowerCase()
                  .replace(" ", "-")}">${item.type}</span></td>
                <td>${this.formatCurrency(item.amount)}</td>
                <td>${this.formatCurrency(item.currentValue)}</td>
                <td class="metric-change ${gainLossClass}">${this.formatCurrency(
        gainLoss
      )}</td>
                <td class="metric-change ${gainLossClass}">${returnPercent.toFixed(
        2
      )}%</td>
                <td>${this.formatDate(item.date)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" onclick="financeManager.editInvestment('${
                          item.id
                        }')">Edit</button>
                        <button class="action-btn delete-btn" onclick="financeManager.deleteInvestment('${
                          item.id
                        }')">Delete</button>
                    </div>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  // PDF Processing Methods
  handleFileSelect(e) {
    const file = e.target.files[0];
    if (file && file.type === "application/pdf") {
      this.selectedFile = file;
      this.showFileSelected(file);
      document.getElementById("processPdfBtn").disabled = false;
    } else {
      this.showToast("Please select a valid PDF file", "error");
    }
  }

  handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById("fileUploadArea").classList.add("dragover");
  }

  handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById("fileUploadArea").classList.remove("dragover");

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type === "application/pdf") {
        this.selectedFile = file;
        this.showFileSelected(file);
        document.getElementById("processPdfBtn").disabled = false;
      } else {
        this.showToast("Please drop a valid PDF file", "error");
      }
    }
  }

  handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById("fileUploadArea").classList.remove("dragover");
  }

  showFileSelected(file) {
    document.getElementById("uploadPlaceholder").style.display = "none";
    document.getElementById("fileSelected").style.display = "flex";
    document.getElementById("fileName").textContent = file.name;
    document.getElementById("fileSize").textContent = this.formatFileSize(
      file.size
    );
    document.getElementById("passwordSection").style.display = "block";
  }

  changeFile() {
    document.getElementById("uploadPlaceholder").style.display = "flex";
    document.getElementById("fileSelected").style.display = "none";
    document.getElementById("passwordSection").style.display = "none";
    document.getElementById("pdfFileInput").value = "";
    document.getElementById("processPdfBtn").disabled = true;
    this.selectedFile = null;
  }

  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  async processPDF() {
    if (!this.selectedFile) {
      this.showToast("Please select a PDF file first", "error");
      return;
    }

    const processPdfBtn = document.getElementById("processPdfBtn");
    const btnText = processPdfBtn.querySelector(".btn-text");
    const btnLoader = processPdfBtn.querySelector(".btn-loader");
    const statusDiv = document.getElementById("processingStatus");

    // Show loading state
    processPdfBtn.disabled = true;
    btnText.style.display = "none";
    btnLoader.style.display = "inline";
    statusDiv.textContent = "Processing PDF...";
    statusDiv.className = "processing-status info";

    try {
      const arrayBuffer = await this.selectedFile.arrayBuffer();
      const password = document.getElementById("pdfPassword").value;

      // Load PDF document
      const loadingTask = pdfjsLib.getDocument({
        data: arrayBuffer,
        password: password || undefined,
      });

      this.pdfDocument = await loadingTask.promise;
      statusDiv.textContent = `PDF loaded successfully. ${this.pdfDocument.numPages} pages found.`;

      // Extract text from all pages
      const extractedData = await this.extractTransactionsFromPDF();

      if (extractedData.length > 0) {
        this.extractedTransactions = extractedData;
        this.showPreviewSection();
        this.renderExtractedTransactions();
        statusDiv.textContent = `Successfully extracted ${extractedData.length} transactions.`;
        statusDiv.className = "processing-status success";

        // Show debug info
        this.showDebugInfo(extractedData);
      } else {
        statusDiv.textContent =
          "No transactions found in the PDF. Please check the format or try the debug mode.";
        statusDiv.className = "processing-status error";

        // Show debug button for troubleshooting
        this.showDebugButton();
      }
    } catch (error) {
      console.error("PDF processing error:", error);
      if (error.name === "PasswordException") {
        statusDiv.textContent =
          "Invalid password. Please enter the correct password.";
      } else {
        statusDiv.textContent =
          "Error processing PDF. Please check the file format.";
      }
      statusDiv.className = "processing-status error";
    } finally {
      // Reset button state
      processPdfBtn.disabled = false;
      btnText.style.display = "inline";
      btnLoader.style.display = "none";
    }
  }

  async extractTransactionsFromPDF() {
    const transactions = [];

    try {
      for (let pageNum = 1; pageNum <= this.pdfDocument.numPages; pageNum++) {
        const page = await this.pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Extract text items with position information
        const textItems = textContent.items.map((item) => ({
          text: item.str,
          x: item.transform[4],
          y: item.transform[5],
          width: item.width,
          height: item.height,
        }));

        // Debug: Log first few items to understand structure
        if (pageNum === 1) {
          console.log("First 20 text items:", textItems.slice(0, 20));
        }

        const pageTransactions =
          this.parseTransactionsFromTextWithPosition(textItems);
        transactions.push(...pageTransactions);
      }
    } catch (error) {
      console.error("Error extracting text from PDF:", error);
    }

    return transactions;
  }

  parseTransactionsFromTextWithPosition(textItems) {
    // First try with position-based parsing
    const positionBasedTransactions = this.parseWithPositions(textItems);

    // If position-based parsing yields good results, use it
    if (positionBasedTransactions.length > 0) {
      console.log(
        "Using position-based parsing, found:",
        positionBasedTransactions.length,
        "transactions"
      );
      return positionBasedTransactions;
    }

    // Fallback to text-only parsing
    const textOnlyItems = textItems.map((item) => item.text);
    return this.parseTransactionsFromText(textOnlyItems);
  }

  parseWithPositions(textItems) {
    const transactions = [];
    const dateRegex = /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/;
    const amountRegex = /₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    // Group items by approximate Y position (rows)
    const rows = this.groupItemsByRows(textItems);

    // Detect bank type from all text
    const allText = textItems.map((item) => item.text).join(" ");
    const bankType = this.detectBankType([allText]);

    console.log("Position-based parsing for bank:", bankType);
    console.log("Found", rows.length, "rows");

    for (const row of rows) {
      const rowText = row.map((item) => item.text).join(" ");

      // Look for date in this row
      const dateMatch = rowText.match(dateRegex);
      if (dateMatch) {
        const transaction = this.parseRowTransaction(row, bankType);
        if (transaction) {
          transactions.push(transaction);
        }
      }
    }

    return transactions;
  }

  groupItemsByRows(textItems, tolerance = 5) {
    const rows = [];
    const sortedItems = [...textItems].sort((a, b) => b.y - a.y); // Sort by Y position (top to bottom)

    for (const item of sortedItems) {
      // Find existing row with similar Y position
      let foundRow = rows.find(
        (row) => Math.abs(row[0].y - item.y) <= tolerance
      );

      if (foundRow) {
        foundRow.push(item);
      } else {
        rows.push([item]);
      }
    }

    // Sort items within each row by X position (left to right)
    rows.forEach((row) => {
      row.sort((a, b) => a.x - b.x);
    });

    return rows;
  }

  parseRowTransaction(row, bankType) {
    const rowTexts = row.map((item) => item.text);
    const rowText = rowTexts.join(" ");

    const dateRegex = /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/;
    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    const dateMatch = rowText.match(dateRegex);
    if (!dateMatch) return null;

    let description = "";
    let amount = 0;

    // Bank-specific row parsing
    switch (bankType) {
      case "HDFC":
        return this.parseHDFCRow(rowTexts, dateMatch[1]);
      case "ICICI":
        return this.parseICICIRow(rowTexts, dateMatch[1]);
      case "KOTAK":
        return this.parseKotakRow(rowTexts, dateMatch[1]);
      default:
        return this.parseGenericRow(rowTexts, dateMatch[1]);
    }
  }

  parseHDFCRow(rowTexts, dateStr) {
    // HDFC format: Date | Description | Category | Amount
    let description = "";
    let amount = 0;
    let category = "";

    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    for (let i = 0; i < rowTexts.length; i++) {
      const text = rowTexts[i].trim();

      if (text === dateStr) continue; // Skip date

      const amountMatch = text.match(amountRegex);
      if (amountMatch && parseFloat(amountMatch[1].replace(/,/g, "")) > 0) {
        amount = parseFloat(amountMatch[1].replace(/,/g, ""));
      } else if (this.isCategory(text)) {
        category = text;
      } else if (text.length > 2) {
        description += (description ? " " : "") + text;
      }
    }

    if (description && amount > 0) {
      return {
        id: this.generateId(),
        date: this.parseDate(dateStr),
        description: this.cleanDescription(description),
        amount: amount,
        category: category || this.suggestCategory(description),
        selected: true,
      };
    }

    return null;
  }

  parseICICIRow(rowTexts, dateStr) {
    // ICICI format: Date | SerNo | Transaction Details | Amount
    let description = "";
    let amount = 0;

    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;
    let skipNext = false;

    for (let i = 0; i < rowTexts.length; i++) {
      const text = rowTexts[i].trim();

      if (text === dateStr) continue; // Skip date
      if (skipNext) {
        skipNext = false;
        continue;
      }

      // Skip serial numbers
      if (/^\d+$/.test(text) && text.length <= 3) {
        skipNext = true;
        continue;
      }

      const amountMatch = text.match(amountRegex);
      if (amountMatch) {
        const potentialAmount = parseFloat(amountMatch[1].replace(/,/g, ""));
        if (potentialAmount > 0 && potentialAmount < 1000000) {
          amount = potentialAmount;
        }
      } else if (text.length > 2) {
        description += (description ? " " : "") + text;
      }
    }

    if (description && amount > 0) {
      return {
        id: this.generateId(),
        date: this.parseDate(dateStr),
        description: this.cleanDescription(description),
        amount: amount,
        category: this.suggestCategory(description),
        selected: true,
      };
    }

    return null;
  }

  parseKotakRow(rowTexts, dateStr) {
    // Kotak format: Date | Transaction Details | Amount
    let description = "";
    let amount = 0;

    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    for (let i = 0; i < rowTexts.length; i++) {
      const text = rowTexts[i].trim();

      if (text === dateStr) continue; // Skip date

      const amountMatch = text.match(amountRegex);
      if (amountMatch) {
        const potentialAmount = parseFloat(amountMatch[1].replace(/,/g, ""));
        if (potentialAmount > 0 && potentialAmount < 1000000) {
          amount = potentialAmount;
        }
      } else if (text.length > 2) {
        description += (description ? " " : "") + text;
      }
    }

    if (description && amount > 0) {
      return {
        id: this.generateId(),
        date: this.parseDate(dateStr),
        description: this.cleanDescription(description),
        amount: amount,
        category: this.suggestCategory(description),
        selected: true,
      };
    }

    return null;
  }

  parseGenericRow(rowTexts, dateStr) {
    // Generic row parsing
    let description = "";
    let amount = 0;

    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    for (let i = 0; i < rowTexts.length; i++) {
      const text = rowTexts[i].trim();

      if (text === dateStr) continue; // Skip date

      const amountMatch = text.match(amountRegex);
      if (amountMatch) {
        const potentialAmount = parseFloat(amountMatch[1].replace(/,/g, ""));
        if (potentialAmount > 0 && potentialAmount < 1000000) {
          amount = potentialAmount;
        }
      } else if (text.length > 2) {
        description += (description ? " " : "") + text;
      }
    }

    if (description && amount > 0) {
      return {
        id: this.generateId(),
        date: this.parseDate(dateStr),
        description: this.cleanDescription(description),
        amount: amount,
        category: this.suggestCategory(description),
        selected: true,
      };
    }

    return null;
  }

  parseTransactionsFromText(textItems) {
    const transactions = [];

    // Enhanced regex patterns for better detection
    const dateRegex = /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/;
    const amountRegex = /₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(?:Cr|Dr)?/i;
    const negativeAmountRegex = /\(₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\)/;

    // Detect bank type from text patterns
    const bankType = this.detectBankType(textItems);
    console.log("Detected bank type:", bankType);

    // Use bank-specific parsing strategy
    switch (bankType) {
      case "HDFC":
        return this.parseHDFCTransactions(textItems);
      case "ICICI":
        return this.parseICICITransactions(textItems);
      case "KOTAK":
        return this.parseKotakTransactions(textItems);
      default:
        return this.parseGenericTransactions(textItems);
    }
  }

  detectBankType(textItems) {
    const text = textItems.join(" ").toUpperCase();

    if (text.includes("HDFC") || text.includes("HDFC BANK")) {
      return "HDFC";
    } else if (text.includes("ICICI") || text.includes("ICICI BANK")) {
      return "ICICI";
    } else if (text.includes("KOTAK") || text.includes("KOTAK MAHINDRA")) {
      return "KOTAK";
    }

    return "GENERIC";
  }

  parseHDFCTransactions(textItems) {
    const transactions = [];
    const dateRegex = /(\d{2}\/\d{2}\/\d{4})/;
    const amountRegex = /₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    // HDFC format: Date | Description | Category | Amount
    for (let i = 0; i < textItems.length - 3; i++) {
      const dateItem = textItems[i];

      if (dateRegex.test(dateItem)) {
        let description = "";
        let category = "";
        let amount = 0;
        let foundAmount = false;

        // Look ahead for description, category, and amount
        for (
          let j = i + 1;
          j < Math.min(i + 10, textItems.length) && !foundAmount;
          j++
        ) {
          const item = textItems[j].trim();

          // Skip empty items
          if (!item) continue;

          // Check if this is an amount
          const amountMatch = item.match(amountRegex);
          if (amountMatch && parseFloat(amountMatch[1].replace(/,/g, "")) > 0) {
            amount = parseFloat(amountMatch[1].replace(/,/g, ""));
            foundAmount = true;
            break;
          }

          // Check if this looks like a category
          if (this.isCategory(item)) {
            category = item;
          } else if (!dateRegex.test(item) && item.length > 2) {
            // Add to description
            description += (description ? " " : "") + item;
          }
        }

        if (description && amount > 0) {
          transactions.push({
            id: this.generateId(),
            date: this.parseDate(dateItem),
            description: this.cleanDescription(description),
            amount: amount,
            category: category || this.suggestCategory(description),
            selected: true,
          });
        }
      }
    }

    return transactions;
  }

  parseICICITransactions(textItems) {
    const transactions = [];
    const dateRegex = /(\d{1,2}\/\d{1,2}\/\d{4})/;
    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    // ICICI format often has: Date | SerNo | Transaction Details | Amount
    for (let i = 0; i < textItems.length - 4; i++) {
      const dateItem = textItems[i];

      if (dateRegex.test(dateItem)) {
        let description = "";
        let amount = 0;
        let foundAmount = false;

        // Look for serial number pattern (optional)
        let startIndex = i + 1;
        if (
          textItems[startIndex] &&
          /^\d+$/.test(textItems[startIndex].trim())
        ) {
          startIndex = i + 2; // Skip serial number
        }

        // Collect description and find amount
        for (
          let j = startIndex;
          j < Math.min(i + 15, textItems.length) && !foundAmount;
          j++
        ) {
          const item = textItems[j].trim();

          if (!item) continue;

          // Check for amount patterns
          const amountMatch = item.match(amountRegex);
          if (amountMatch) {
            const potentialAmount = parseFloat(
              amountMatch[1].replace(/,/g, "")
            );
            if (potentialAmount > 0 && potentialAmount < 1000000) {
              // Reasonable amount range
              amount = potentialAmount;
              foundAmount = true;
              break;
            }
          }

          // Skip if this looks like a date or serial number
          if (!dateRegex.test(item) && !/^\d+$/.test(item) && item.length > 1) {
            description += (description ? " " : "") + item;
          }
        }

        if (description && amount > 0) {
          transactions.push({
            id: this.generateId(),
            date: this.parseDate(dateItem),
            description: this.cleanDescription(description),
            amount: amount,
            category: this.suggestCategory(description),
            selected: true,
          });
        }
      }
    }

    return transactions;
  }

  parseKotakTransactions(textItems) {
    const transactions = [];
    const dateRegex = /(\d{2}\/\d{2}\/\d{4})/;
    const amountRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    // Kotak format: Date | Transaction Details | Amount
    for (let i = 0; i < textItems.length - 2; i++) {
      const dateItem = textItems[i];

      if (dateRegex.test(dateItem)) {
        let description = "";
        let amount = 0;
        let foundAmount = false;

        // Look for transaction details and amount
        for (
          let j = i + 1;
          j < Math.min(i + 12, textItems.length) && !foundAmount;
          j++
        ) {
          const item = textItems[j].trim();

          if (!item) continue;

          // Check for amount
          const amountMatch = item.match(amountRegex);
          if (amountMatch) {
            const potentialAmount = parseFloat(
              amountMatch[1].replace(/,/g, "")
            );
            // Kotak often shows amounts at the end of the row
            if (potentialAmount > 0 && potentialAmount < 1000000) {
              amount = potentialAmount;
              foundAmount = true;
              break;
            }
          }

          // Collect description
          if (!dateRegex.test(item) && item.length > 2) {
            description += (description ? " " : "") + item;
          }
        }

        if (description && amount > 0) {
          transactions.push({
            id: this.generateId(),
            date: this.parseDate(dateItem),
            description: this.cleanDescription(description),
            amount: amount,
            category: this.suggestCategory(description),
            selected: true,
          });
        }
      }
    }

    return transactions;
  }

  parseGenericTransactions(textItems) {
    const transactions = [];
    const dateRegex = /(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/;
    const amountRegex = /₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/;

    // Generic parsing - similar to original but more robust
    for (let i = 0; i < textItems.length - 2; i++) {
      const currentItem = textItems[i];

      if (dateRegex.test(currentItem)) {
        let description = "";
        let amount = 0;
        let foundAmount = false;

        // Look for description and amount in next items
        for (
          let j = i + 1;
          j < Math.min(i + 8, textItems.length) && !foundAmount;
          j++
        ) {
          const item = textItems[j].trim();

          if (!item) continue;

          const amountMatch = item.match(amountRegex);
          if (amountMatch) {
            const potentialAmount = parseFloat(
              amountMatch[1].replace(/,/g, "")
            );
            if (potentialAmount > 0 && potentialAmount < 1000000) {
              amount = potentialAmount;
              foundAmount = true;
              break;
            }
          } else if (!dateRegex.test(item) && item.length > 2) {
            description += (description ? " " : "") + item;
          }
        }

        if (description && amount > 0) {
          transactions.push({
            id: this.generateId(),
            date: this.parseDate(currentItem),
            description: this.cleanDescription(description),
            amount: amount,
            category: this.suggestCategory(description),
            selected: true,
          });
        }
      }
    }

    return transactions;
  }

  isCategory(text) {
    const categories = [
      "Grocery",
      "Food",
      "Fuel",
      "Services",
      "Shopping",
      "Entertainment",
      "Bills",
      "Healthcare",
      "Transport",
      "Travel",
      "Education",
      "Other",
    ];
    return categories.some((cat) =>
      text.toLowerCase().includes(cat.toLowerCase())
    );
  }

  cleanDescription(description) {
    // Remove extra spaces and clean up description
    return description
      .replace(/\s+/g, " ")
      .replace(/[^\w\s\-\.]/g, " ")
      .trim()
      .substring(0, 100);
  }

  parseDate(dateString) {
    // Handle various date formats
    const formats = [
      /(\d{1,2})\/(\d{1,2})\/(\d{2,4})/, // MM/DD/YYYY or DD/MM/YYYY
      /(\d{1,2})-(\d{1,2})-(\d{2,4})/, // MM-DD-YYYY or DD-MM-YYYY
      /(\d{2})\.(\d{2})\.(\d{4})/, // DD.MM.YYYY
    ];

    for (const format of formats) {
      const match = dateString.match(format);
      if (match) {
        let [, part1, part2, part3] = match;

        // Assume DD/MM/YYYY format for Indian context
        const day = parseInt(part1);
        const month = parseInt(part2) - 1; // JavaScript months are 0-indexed
        const year =
          part3.length === 2 ? 2000 + parseInt(part3) : parseInt(part3);

        const date = new Date(year, month, day);
        return date.toISOString().split("T")[0];
      }
    }

    // Fallback to current date
    return new Date().toISOString().split("T")[0];
  }

  suggestCategory(description) {
    const categoryKeywords = {
      Food: [
        "restaurant",
        "cafe",
        "food",
        "dining",
        "pizza",
        "burger",
        "swiggy",
        "zomato",
        "dominos",
        "kfc",
        "mcdonald",
        "subway",
      ],
      Grocery: [
        "grocery",
        "supermarket",
        "bigbasket",
        "grofers",
        "blinkit",
        "zepto",
        "dunzo",
        "fresh",
        "mart",
        "store",
        "vegetables",
        "fruits",
      ],
      Transport: [
        "uber",
        "ola",
        "taxi",
        "metro",
        "bus",
        "parking",
        "auto",
        "rickshaw",
      ],
      Fuel: [
        "petrol",
        "fuel",
        "diesel",
        "gas",
        "pump",
        "hp",
        "iocl",
        "bpcl",
        "shell",
      ],
      Shopping: [
        "amazon",
        "flipkart",
        "mall",
        "store",
        "shop",
        "purchase",
        "buy",
      ],
      Bills: [
        "electricity",
        "water",
        "gas",
        "internet",
        "mobile",
        "phone",
        "utility",
      ],
      Entertainment: [
        "movie",
        "cinema",
        "netflix",
        "spotify",
        "game",
        "entertainment",
      ],
      Healthcare: [
        "hospital",
        "doctor",
        "medical",
        "pharmacy",
        "health",
        "clinic",
      ],
      Travel: ["hotel", "flight", "train", "booking", "travel", "trip"],
      Education: [
        "school",
        "college",
        "course",
        "book",
        "education",
        "tuition",
      ],
    };

    const lowerDescription = description.toLowerCase();

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (keywords.some((keyword) => lowerDescription.includes(keyword))) {
        return category;
      }
    }

    return "Other";
  }

  showPreviewSection() {
    document.getElementById("previewSection").style.display = "block";
  }

  renderExtractedTransactions() {
    const tbody = document.getElementById("extractedTransactions");
    tbody.innerHTML = "";

    this.extractedTransactions.forEach((transaction, index) => {
      const row = document.createElement("tr");
      row.innerHTML = `
                <td>
                    <input type="checkbox" ${
                      transaction.selected ? "checked" : ""
                    }
                           onchange="financeManager.toggleTransactionSelection(${index})">
                </td>
                <td>${this.formatDate(transaction.date)}</td>
                <td>${transaction.description}</td>
                <td>${this.formatCurrency(transaction.amount)}</td>
                <td>
                    <select class="category-select" onchange="financeManager.updateTransactionCategory(${index}, this.value)">
                        <option value="Food" ${
                          transaction.category === "Food" ? "selected" : ""
                        }>Food</option>
                        <option value="Grocery" ${
                          transaction.category === "Grocery" ? "selected" : ""
                        }>Grocery</option>
                        <option value="Transport" ${
                          transaction.category === "Transport" ? "selected" : ""
                        }>Transport</option>
                        <option value="Fuel" ${
                          transaction.category === "Fuel" ? "selected" : ""
                        }>Fuel</option>
                        <option value="Entertainment" ${
                          transaction.category === "Entertainment"
                            ? "selected"
                            : ""
                        }>Entertainment</option>
                        <option value="Shopping" ${
                          transaction.category === "Shopping" ? "selected" : ""
                        }>Shopping</option>
                        <option value="Bills" ${
                          transaction.category === "Bills" ? "selected" : ""
                        }>Bills</option>
                        <option value="Healthcare" ${
                          transaction.category === "Healthcare"
                            ? "selected"
                            : ""
                        }>Healthcare</option>
                        <option value="Education" ${
                          transaction.category === "Education" ? "selected" : ""
                        }>Education</option>
                        <option value="Travel" ${
                          transaction.category === "Travel" ? "selected" : ""
                        }>Travel</option>
                        <option value="Other" ${
                          transaction.category === "Other" ? "selected" : ""
                        }>Other</option>
                    </select>
                </td>
                <td>
                    <button class="action-btn delete-btn" onclick="financeManager.removeExtractedTransaction(${index})">Remove</button>
                </td>
            `;
      tbody.appendChild(row);
    });
  }

  toggleTransactionSelection(index) {
    this.extractedTransactions[index].selected =
      !this.extractedTransactions[index].selected;
  }

  updateTransactionCategory(index, category) {
    const transaction = this.extractedTransactions[index];
    const oldCategory = transaction.category;
    transaction.category = category;

    // Learn from this categorization
    this.learnFromCategorization(transaction.description, category);

    // Apply learned categorization to other transactions in this session
    this.applyLearnedCategorization();

    // Show visual feedback if category was changed
    if (oldCategory !== category) {
      this.showCategoryChangeNotification(transaction.description, category);
    }
  }

  learnFromCategorization(description, category) {
    // Extract keywords from the description
    const keywords = this.extractKeywords(description);

    // Store the learning for this session
    keywords.forEach((keyword) => {
      if (!this.categoryLearning[keyword]) {
        this.categoryLearning[keyword] = {};
      }
      this.categoryLearning[keyword][category] =
        (this.categoryLearning[keyword][category] || 0) + 1;
    });
  }

  extractKeywords(description) {
    // Extract meaningful keywords from description
    const words = description
      .toLowerCase()
      .replace(/[^\w\s]/g, " ")
      .split(/\s+/)
      .filter((word) => word.length > 2); // Only words longer than 2 characters

    // Remove common words
    const stopWords = [
      "the",
      "and",
      "for",
      "are",
      "but",
      "not",
      "you",
      "all",
      "can",
      "had",
      "her",
      "was",
      "one",
      "our",
      "out",
      "day",
      "get",
      "has",
      "him",
      "his",
      "how",
      "man",
      "new",
      "now",
      "old",
      "see",
      "two",
      "way",
      "who",
      "boy",
      "did",
      "its",
      "let",
      "put",
      "say",
      "she",
      "too",
      "use",
    ];

    return words.filter((word) => !stopWords.includes(word));
  }

  applyLearnedCategorization() {
    let changesApplied = 0;

    this.extractedTransactions.forEach((transaction, index) => {
      const suggestedCategory = this.getLearnedCategory(
        transaction.description
      );
      if (suggestedCategory && suggestedCategory !== transaction.category) {
        transaction.category = suggestedCategory;
        changesApplied++;

        // Add visual indicator for auto-categorized transactions
        transaction.autoLearned = true;
      }
    });

    if (changesApplied > 0) {
      this.renderExtractedTransactions();
      this.showToast(
        `Auto-categorized ${changesApplied} similar transactions based on your input!`,
        "success"
      );
    }
  }

  getLearnedCategory(description) {
    const keywords = this.extractKeywords(description);
    const categoryScores = {};

    keywords.forEach((keyword) => {
      if (this.categoryLearning[keyword]) {
        Object.entries(this.categoryLearning[keyword]).forEach(
          ([category, count]) => {
            categoryScores[category] = (categoryScores[category] || 0) + count;
          }
        );
      }
    });

    // Return the category with the highest score
    let bestCategory = null;
    let bestScore = 0;

    Object.entries(categoryScores).forEach(([category, score]) => {
      if (score > bestScore) {
        bestCategory = category;
        bestScore = score;
      }
    });

    return bestCategory;
  }

  showCategoryChangeNotification(description, category) {
    // Brief highlight effect for the changed transaction
    const shortDesc =
      description.substring(0, 30) + (description.length > 30 ? "..." : "");
    this.showToast(`Learned: "${shortDesc}" → ${category}`, "info");
  }

  removeExtractedTransaction(index) {
    this.extractedTransactions.splice(index, 1);
    this.renderExtractedTransactions();
  }

  selectAllTransactions() {
    this.extractedTransactions.forEach(
      (transaction) => (transaction.selected = true)
    );
    this.renderExtractedTransactions();
  }

  deselectAllTransactions() {
    this.extractedTransactions.forEach(
      (transaction) => (transaction.selected = false)
    );
    this.renderExtractedTransactions();
  }

  importSelectedTransactions() {
    const selectedTransactions = this.extractedTransactions.filter(
      (t) => t.selected
    );

    if (selectedTransactions.length === 0) {
      this.showToast(
        "Please select at least one transaction to import",
        "warning"
      );
      return;
    }

    // Convert to expense format and add to expenses
    selectedTransactions.forEach((transaction) => {
      const expense = {
        id: this.generateId(),
        name: transaction.description,
        amount: transaction.amount,
        category: transaction.category,
        date: transaction.date,
        timestamp: new Date().toISOString(),
        source: "Credit Card Import",
        selected: false, // Add selection state for bulk operations
      };
      this.data.expenses.push(expense);
    });

    // Add to import history
    const importRecord = {
      id: this.generateId(),
      filename: this.selectedFile.name,
      date: new Date().toISOString(),
      transactionsCount: selectedTransactions.length,
      totalAmount: selectedTransactions.reduce((sum, t) => sum + t.amount, 0),
    };
    this.data.importHistory.push(importRecord);

    this.saveData();
    this.updateAllMetrics();
    this.showToast(
      `Successfully imported ${selectedTransactions.length} transactions!`,
      "success"
    );

    // Reset the import interface
    this.resetImportInterface();
  }

  resetImportInterface() {
    document.getElementById("previewSection").style.display = "none";
    this.changeFile();
    this.extractedTransactions = [];
    this.categoryLearning = {}; // Reset category learning for new import session
    document.getElementById("processingStatus").textContent = "";
    this.renderImportHistory();
  }

  renderImportHistory() {
    const historyList = document.getElementById("importHistory");
    historyList.innerHTML = "";

    if (this.data.importHistory.length === 0) {
      historyList.innerHTML =
        '<p style="text-align: center; color: var(--gray-500); padding: 2rem;">No import history yet</p>';
      return;
    }

    this.data.importHistory.reverse().forEach((record) => {
      const historyItem = document.createElement("div");
      historyItem.className = "history-item";
      historyItem.innerHTML = `
                <div class="history-info">
                    <div class="history-filename">${record.filename}</div>
                    <div class="history-date">${this.formatDate(
                      record.date
                    )}</div>
                </div>
                <div class="history-stats">
                    <span>${record.transactionsCount} transactions</span>
                    <span>${this.formatCurrency(record.totalAmount)}</span>
                </div>
            `;
      historyList.appendChild(historyItem);
    });
  }

  // Budget Methods
  openBudgetModal() {
    document.getElementById("budgetModal").classList.add("active");
    if (this.data.budget.monthly) {
      document.getElementById("budgetAmount").value = this.data.budget.monthly;
    }
  }

  closeBudgetModal() {
    document.getElementById("budgetModal").classList.remove("active");
  }

  updateBudgetDisplay() {
    const monthlyBudget = this.data.budget.monthly || 0;
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyExpenses = this.data.expenses
      .filter((item) => {
        const itemDate = new Date(item.date);
        return (
          itemDate.getMonth() === currentMonth &&
          itemDate.getFullYear() === currentYear
        );
      })
      .reduce((sum, item) => sum + item.amount, 0);

    const budgetUsed =
      monthlyBudget > 0 ? (monthlyExpenses / monthlyBudget) * 100 : 0;

    document.getElementById("monthlyBudget").textContent =
      this.formatCurrency(monthlyBudget);
    document.getElementById("budgetProgress").style.width = `${Math.min(
      budgetUsed,
      100
    )}%`;
    document.getElementById(
      "budgetProgressText"
    ).textContent = `${budgetUsed.toFixed(1)}% used`;

    // Update progress bar color based on usage
    const progressBar = document.getElementById("budgetProgress");
    if (budgetUsed > 90) {
      progressBar.style.background = "var(--danger-color)";
    } else if (budgetUsed > 75) {
      progressBar.style.background = "var(--warning-color)";
    } else {
      progressBar.style.background =
        "linear-gradient(90deg, var(--success-color), var(--warning-color))";
    }
  }

  renderCategoryBudgets() {
    const categoryBudgets = document.getElementById("categoryBudgets");
    const categories = [
      "Food",
      "Transport",
      "Entertainment",
      "Shopping",
      "Bills",
      "Healthcare",
      "Education",
      "Travel",
      "Other",
    ];

    categoryBudgets.innerHTML = "";

    categories.forEach((category) => {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();

      const categoryExpenses = this.data.expenses
        .filter((item) => {
          const itemDate = new Date(item.date);
          return (
            item.category === category &&
            itemDate.getMonth() === currentMonth &&
            itemDate.getFullYear() === currentYear
          );
        })
        .reduce((sum, item) => sum + item.amount, 0);

      const budgetCard = document.createElement("div");
      budgetCard.className = "budget-category-card";
      budgetCard.innerHTML = `
                <div class="budget-category-header">
                    <span class="budget-category-name">${category}</span>
                    <span class="budget-category-amount">${this.formatCurrency(
                      categoryExpenses
                    )}</span>
                </div>
                <div class="budget-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${Math.min(
                          (categoryExpenses / 10000) * 100,
                          100
                        )}%"></div>
                    </div>
                </div>
            `;
      categoryBudgets.appendChild(budgetCard);
    });
  }

  // Chart Methods (simplified for now)
  renderCharts() {
    this.renderExpenseChart();
    this.renderIncomeExpenseChart();
    this.renderNetWorthChart();
    this.renderPortfolioChart();
  }

  renderExpenseChart() {
    const ctx = document.getElementById("expenseChart").getContext("2d");

    // Destroy existing chart if it exists
    if (this.charts.expenseChart) {
      this.charts.expenseChart.destroy();
    }

    const categories = [
      "Food",
      "Transport",
      "Entertainment",
      "Shopping",
      "Bills",
      "Healthcare",
      "Education",
      "Travel",
      "Other",
    ];
    const data = categories.map((category) =>
      this.data.expenses
        .filter((expense) => expense.category === category)
        .reduce((sum, expense) => sum + expense.amount, 0)
    );

    this.charts.expenseChart = new Chart(ctx, {
      type: "doughnut",
      data: {
        labels: categories,
        datasets: [
          {
            data: data,
            backgroundColor: [
              "#ef4444",
              "#3b82f6",
              "#8b5cf6",
              "#10b981",
              "#f59e0b",
              "#06b6d4",
              "#84cc16",
              "#f97316",
              "#6b7280",
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  renderIncomeExpenseChart() {
    const ctx = document.getElementById("incomeExpenseChart").getContext("2d");

    if (this.charts.incomeExpenseChart) {
      this.charts.incomeExpenseChart.destroy();
    }

    // Get last 6 months data
    const months = [];
    const incomeData = [];
    const expenseData = [];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const month = date.getMonth();
      const year = date.getFullYear();

      months.push(date.toLocaleDateString("en-IN", { month: "short" }));

      const monthIncome = this.data.income
        .filter((item) => {
          const itemDate = new Date(item.date);
          return (
            itemDate.getMonth() === month && itemDate.getFullYear() === year
          );
        })
        .reduce((sum, item) => sum + item.amount, 0);

      const monthExpense = this.data.expenses
        .filter((item) => {
          const itemDate = new Date(item.date);
          return (
            itemDate.getMonth() === month && itemDate.getFullYear() === year
          );
        })
        .reduce((sum, item) => sum + item.amount, 0);

      incomeData.push(monthIncome);
      expenseData.push(monthExpense);
    }

    this.charts.incomeExpenseChart = new Chart(ctx, {
      type: "bar",
      data: {
        labels: months,
        datasets: [
          {
            label: "Income",
            data: incomeData,
            backgroundColor: "#10b981",
          },
          {
            label: "Expenses",
            data: expenseData,
            backgroundColor: "#ef4444",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  renderNetWorthChart() {
    const ctx = document.getElementById("netWorthChart").getContext("2d");

    if (this.charts.netWorthChart) {
      this.charts.netWorthChart.destroy();
    }

    // Simplified net worth trend (last 6 months)
    const months = [];
    const netWorthData = [];

    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push(date.toLocaleDateString("en-IN", { month: "short" }));

      // Simplified calculation
      const totalIncome = this.data.income.reduce(
        (sum, item) => sum + item.amount,
        0
      );
      const totalExpenses = this.data.expenses.reduce(
        (sum, item) => sum + item.amount,
        0
      );
      const totalInvestments = this.data.investments.reduce(
        (sum, item) => sum + item.currentValue,
        0
      );

      netWorthData.push(totalIncome - totalExpenses + totalInvestments);
    }

    this.charts.netWorthChart = new Chart(ctx, {
      type: "line",
      data: {
        labels: months,
        datasets: [
          {
            label: "Net Worth",
            data: netWorthData,
            borderColor: "#3b82f6",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            fill: true,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  renderPortfolioChart() {
    const ctx = document.getElementById("portfolioChart").getContext("2d");

    if (this.charts.portfolioChart) {
      this.charts.portfolioChart.destroy();
    }

    const types = [
      "Stocks",
      "Mutual Funds",
      "Fixed Deposits",
      "Crypto",
      "Bonds",
      "Real Estate",
    ];
    const data = types.map((type) =>
      this.data.investments
        .filter((investment) => investment.type === type)
        .reduce((sum, investment) => sum + investment.currentValue, 0)
    );

    this.charts.portfolioChart = new Chart(ctx, {
      type: "pie",
      data: {
        labels: types,
        datasets: [
          {
            data: data,
            backgroundColor: [
              "#ef4444",
              "#3b82f6",
              "#10b981",
              "#f59e0b",
              "#8b5cf6",
              "#06b6d4",
            ],
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  renderRecentTransactions() {
    const recentTransactions = document.getElementById("recentTransactions");

    // Combine all transactions and sort by date
    const allTransactions = [
      ...this.data.income.map((item) => ({ ...item, type: "income" })),
      ...this.data.expenses.map((item) => ({ ...item, type: "expense" })),
    ]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    recentTransactions.innerHTML = "";

    if (allTransactions.length === 0) {
      recentTransactions.innerHTML =
        '<p style="text-align: center; color: var(--gray-500); padding: 2rem;">No transactions yet</p>';
      return;
    }

    allTransactions.forEach((transaction) => {
      const transactionItem = document.createElement("div");
      transactionItem.className = "transaction-item";

      const name =
        transaction.type === "income" ? transaction.source : transaction.name;
      const amountClass = transaction.type === "income" ? "income" : "expense";
      const amountPrefix = transaction.type === "income" ? "+" : "-";

      transactionItem.innerHTML = `
                <div class="transaction-info">
                    <div class="transaction-name">${name}</div>
                    <div class="transaction-category">${
                      transaction.category
                    } • ${this.formatDate(transaction.date)}</div>
                </div>
                <div class="transaction-amount ${amountClass}">${amountPrefix}${this.formatCurrency(
        transaction.amount
      )}</div>
            `;
      recentTransactions.appendChild(transactionItem);
    });
  }

  // Edit methods (simplified for now)
  editIncome(id) {
    this.showToast("Edit functionality coming soon!", "info");
  }

  editExpense(id) {
    const expense = this.data.expenses.find((item) => item.id === id);
    if (expense) {
      this.populateExpenseFormForEdit(expense);
    } else {
      this.showToast("Expense not found!", "error");
    }
  }

  populateExpenseFormForEdit(expense) {
    // Set edit mode
    this.isEditMode = true;
    this.editingExpenseId = expense.id;

    // Pre-populate the form with current expense data
    document.getElementById("expenseName").value = expense.name;
    document.getElementById("expenseAmount").value = expense.amount;
    document.getElementById("expenseCategory").value = expense.category;
    document.getElementById("expenseDate").value = expense.date;

    // Update UI to show edit mode
    this.updateFormForEditMode();

    // Scroll to form
    document
      .querySelector(".form-section")
      .scrollIntoView({ behavior: "smooth" });

    this.showToast("Expense loaded for editing", "info");
  }

  updateFormForEditMode() {
    const formSection = document.querySelector(".form-section");
    const submitBtn = document.getElementById("expenseSubmitBtn");
    const cancelBtn = document.getElementById("cancelEditExpenseBtn");

    // Add edit mode styling
    formSection.classList.add("edit-mode");

    // Update button text and visibility
    submitBtn.textContent = "Update Expense";
    cancelBtn.style.display = "inline-block";
  }

  cancelEditExpense() {
    this.resetExpenseForm();
  }

  resetExpenseForm() {
    // Reset edit mode
    this.isEditMode = false;
    this.editingExpenseId = null;

    // Clear form
    document.getElementById("expenseForm").reset();
    this.setDefaultDates();

    // Update UI to show add mode
    const formSection = document.querySelector(".form-section");
    const submitBtn = document.getElementById("expenseSubmitBtn");
    const cancelBtn = document.getElementById("cancelEditExpenseBtn");

    // Remove edit mode styling
    formSection.classList.remove("edit-mode");

    // Update button text and visibility
    submitBtn.textContent = "Add Expense";
    cancelBtn.style.display = "none";
  }

  editInvestment(id) {
    this.showToast("Edit functionality coming soon!", "info");
  }

  // Debug helper methods
  showDebugInfo(extractedData) {
    console.log("=== PDF PARSING DEBUG INFO ===");
    console.log("Total transactions extracted:", extractedData.length);
    console.log("Sample transactions:", extractedData.slice(0, 3));
    console.log("Bank type detected:", this.detectBankType([]));
    console.log("===============================");
  }

  showDebugButton() {
    const statusDiv = document.getElementById("processingStatus");
    const debugBtn = document.createElement("button");
    debugBtn.textContent = "Show Debug Info";
    debugBtn.className = "btn btn-secondary";
    debugBtn.style.marginTop = "10px";
    debugBtn.onclick = () => this.showDetailedDebugInfo();

    statusDiv.appendChild(document.createElement("br"));
    statusDiv.appendChild(debugBtn);
  }

  async showDetailedDebugInfo() {
    if (!this.pdfDocument) return;

    console.log("=== DETAILED PDF DEBUG INFO ===");

    try {
      const page = await this.pdfDocument.getPage(1);
      const textContent = await page.getTextContent();

      console.log("Total text items on page 1:", textContent.items.length);
      console.log("First 50 text items:");

      textContent.items.slice(0, 50).forEach((item, index) => {
        console.log(
          `${index}: "${item.str}" at (${item.transform[4]}, ${item.transform[5]})`
        );
      });

      // Try to identify table structure
      const textItems = textContent.items.map((item) => ({
        text: item.str,
        x: item.transform[4],
        y: item.transform[5],
      }));

      const rows = this.groupItemsByRows(textItems);
      console.log("Detected rows:", rows.length);
      console.log("First 5 rows:");

      rows.slice(0, 5).forEach((row, index) => {
        console.log(`Row ${index}:`, row.map((item) => item.text).join(" | "));
      });
    } catch (error) {
      console.error("Debug info error:", error);
    }

    console.log("================================");
    this.showToast(
      "Debug information logged to console. Press F12 to view.",
      "info"
    );
  }
}

// Initialize the application
const financeManager = new FinanceManager();
