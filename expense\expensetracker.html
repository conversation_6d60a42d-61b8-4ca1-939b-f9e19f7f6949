<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Expense Tracker - Modern Financial Management</title>
    <link rel="stylesheet" href="expensetrackers.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container">
      <h2>💰 Expense Tracker</h2>

      <!-- Three-Card Layout: Starting Budget, Total Expenses, Balance -->
      <div class="three-card-container">
        <div class="summary-card income">
          <h4>Starting Budget</h4>
          <p id="money-plus" class="money money-plus">+₹10,000.00</p>
        </div>
        <div class="summary-card expense">
          <h4>Total Expenses</h4>
          <p id="money-minus" class="money money-minus">-₹0.00</p>
        </div>
        <div class="summary-card balance">
          <h4>Balance</h4>
          <p id="balance" class="money balance-amount">₹0.00</p>
        </div>
      </div>

      <!-- Add New Expense Form -->
      <h3>➕ Add New Expense</h3>
      <div class="form-card">
        <form id="form">
          <div class="form-row">
            <div class="form-control">
              <label for="text">Expense Name</label>
              <input
                type="text"
                id="text"
                placeholder="Enter expense name"
                required
              />
            </div>
            <div class="form-control">
              <label for="amount">Amount (₹)</label>
              <input
                type="number"
                id="amount"
                placeholder="Enter amount"
                step="1.00"
                min="0"
                required
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-control">
              <label for="category">Category</label>
              <select id="category" required>
                <option value="">Select Category</option>
                <option value="Food">Food</option>
                <option value="Transport">Transport</option>
                <option value="Entertainment">Entertainment</option>
                <option value="Shopping">Shopping</option>
                <option value="Bills">Bills</option>
                <option value="Healthcare">Healthcare</option>
                <option value="Education">Education</option>
                <option value="Travel">Travel</option>
                <option value="Other">Other</option>
              </select>
            </div>
            <div class="form-control">
              <label for="date">Date</label>
              <input type="date" id="date" required />
            </div>
          </div>
          <button type="submit" class="btn">Add Expense</button>
        </form>
      </div>

      <!-- Expense History -->
      <div class="history-header">
        <h3>📊 Expense History</h3>
        <div class="filter-controls">
          <label for="categoryFilter">Filter by Category:</label>
          <select id="categoryFilter">
            <option value="All">All</option>
            <option value="Food">Food</option>
            <option value="Transport">Transport</option>
            <option value="Entertainment">Entertainment</option>
            <option value="Shopping">Shopping</option>
            <option value="Bills">Bills</option>
            <option value="Healthcare">Healthcare</option>
            <option value="Education">Education</option>
            <option value="Travel">Travel</option>
            <option value="Other">Other</option>
          </select>
        </div>
      </div>

      <div class="transaction-container">
        <div class="table-container">
          <table id="expenseTable" class="expense-table">
            <thead>
              <tr>
                <th>Expense Name</th>
                <th>Amount</th>
                <th>Category</th>
                <th>Date</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody id="list">
              <!-- Transactions will be dynamically added here -->
            </tbody>
          </table>
          <div id="empty-state" class="empty-state" style="display: none">
            <div class="empty-state-icon">📝</div>
            <div class="empty-state-text">No expenses yet</div>
            <div class="empty-state-subtext">
              Add your first expense above to get started
            </div>
          </div>
        </div>
        <div class="total-display">
          <strong>Total: <span id="totalAmount">₹0.00</span></strong>
        </div>
      </div>
    </div>

    <script src="trackerscript.js"></script>
  </body>
</html>
