# Expense Tracker Web App

This Expense Tracker Web App is a simple tool built with HTML, CSS, and JavaScript to help users manage their finances effectively. Keep track of your income and expenses, add new transactions, and visualize your financial health.

## Features
- **Transaction Management:** Add income or expenses with ease by entering the transaction details.
- **Dynamic Updates:** Real-time updates of balance, income, and expenses displayed on the interface.
- **Transaction Removal:** Remove unwanted transactions seamlessly with a delete button.

## Usage
1. Open the web app in your browser.
2. Enter the transaction details, including the transaction text and amount.
3. Press the "Add Transaction" button to update the list and balances.
4. View your current balance, income, and expenses.
5. Delete transactions by clicking the "x" button next to each entry.

## Technologies Used
- **HTML:** Structure the web page.
- **CSS:** Style the user interface for a visually appealing experience.
- **JavaScript:** Implement the dynamic functionality of the Expense Tracker.

## Code Overview
- The JavaScript code handles transaction addition, deletion, and updates to the local storage.
- Transactions are stored locally to maintain data persistence between sessions.

## How to Run
Simply open the `index.html` file in a web browser to start using the Expense Tracker.

Feel free to enhance and customize the code to suit your preferences or integrate additional features!
