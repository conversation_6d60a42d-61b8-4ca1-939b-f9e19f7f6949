/* Modern CSS Variables for Design System */
:root {
  /* Color Palette */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --success-light: #34d399;
  --danger-color: #ef4444;
  --danger-light: #f87171;
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Background Colors */
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;

  /* Spacing Scale */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Font Family */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;

  /* Sidebar */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--gray-100) 100%
  );
  color: var(--gray-800);
  line-height: 1.6;
  overflow-x: hidden;
}

/* App Container */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background: var(--white);
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  overflow: hidden;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.sidebar-toggle {
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background: var(--gray-100);
}

.sidebar-toggle span {
  width: 20px;
  height: 2px;
  background: var(--gray-600);
  border-radius: 1px;
  transition: all var(--transition-fast);
}

.sidebar.collapsed .sidebar-toggle span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.sidebar.collapsed .sidebar-toggle span:nth-child(2) {
  opacity: 0;
}

.sidebar.collapsed .sidebar-toggle span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.sidebar-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  white-space: nowrap;
  transition: all var(--transition-normal);
}

.sidebar.collapsed .sidebar-title {
  opacity: 0;
  transform: translateX(-20px);
}

/* Navigation Menu */
.nav-menu {
  list-style: none;
  padding: var(--spacing-4);
}

.nav-item {
  margin-bottom: var(--spacing-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  color: var(--gray-600);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-weight: 500;
}

.nav-link:hover {
  background: var(--gray-100);
  color: var(--gray-900);
  transform: translateX(4px);
}

.nav-link.active {
  background: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.nav-link.active:hover {
  background: var(--primary-dark);
  transform: translateX(0);
}

.nav-icon {
  font-size: var(--font-size-lg);
  min-width: 24px;
  text-align: center;
}

.nav-text {
  white-space: nowrap;
  transition: all var(--transition-normal);
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  transform: translateX(-20px);
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: var(--spacing-4) var(--spacing-2);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  padding: var(--spacing-8);
  transition: all var(--transition-normal);
  min-height: 100vh;
}

.sidebar.collapsed + .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* Page Styles */
.page {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.page.active {
  display: block;
}

.page-header {
  margin-bottom: var(--spacing-8);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.page-header p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.metric-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-fast);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-icon {
  font-size: var(--font-size-3xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
}

.metric-content h3 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-1);
}

.metric-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.metric-change {
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--danger-color);
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.chart-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.chart-card h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
}

.chart-card canvas {
  max-height: 300px;
}

/* Analytics Grid */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.analytics-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  text-align: center;
}

.analytics-card h4 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-2);
}

.analytics-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
}

/* Form Styles */
.form-section {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-8);
}

.form-section h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
}

.transaction-form {
  max-width: 800px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
}

.form-group input,
.form-group select {
  padding: var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  color: var(--gray-800);
  background: var(--white);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  margin-top: var(--spacing-4);
}

.form-actions .btn {
  min-width: 140px;
}

/* Edit Mode Styling */
.form-section.edit-mode {
  border: 2px solid var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.form-section.edit-mode .section-header h3 {
  color: var(--primary-color);
}

.form-section.edit-mode .btn-primary {
  background: #059669;
  border-color: #059669;
}

.form-section.edit-mode .btn-primary:hover {
  background: #047857;
  border-color: #047857;
}

/* Checkbox Styles */
.checkbox-group {
  flex-direction: row;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: var(--font-size-sm);
  font-weight: 700;
}

/* Button Styles */
.btn {
  padding: var(--spacing-4) var(--spacing-6);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  font-family: var(--font-family);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--gray-200);
  color: var(--gray-800);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-300);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: var(--white);
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background: var(--danger-color);
  color: var(--white);
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* History Section */
.history-section {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
}

/* Filter and Total Wrapper */
.filter-total-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-2);
}

.filter-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.filter-controls select {
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background: var(--white);
  color: var(--gray-800);
  min-width: 200px;
}

/* Bulk Controls Section */
.bulk-controls-section {
  margin: var(--spacing-4) 0;
  padding: var(--spacing-3) 0;
  border-top: 1px solid var(--gray-200);
  border-bottom: 1px solid var(--gray-200);
}

.bulk-controls {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
  justify-content: flex-start;
}

.bulk-controls .btn {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-300);
  cursor: pointer;
  transition: all 0.2s ease;
}

.bulk-controls .btn-secondary {
  background: var(--white);
  color: var(--gray-700);
}

.bulk-controls .btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.bulk-controls .btn-danger {
  background: #dc2626;
  color: white;
  border-color: #dc2626;
}

.bulk-controls .btn-danger:hover {
  background: #b91c1c;
  border-color: #b91c1c;
}

/* Filtered Total Display */
.filtered-total-section {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  max-width: fit-content;
  min-width: 300px;
}

.filtered-total-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filtered-total-label {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-700);
}

.filtered-total-amount {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--primary-color);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.data-table th {
  background: var(--gray-50);
  padding: var(--spacing-4);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 2px solid var(--gray-200);
  white-space: nowrap;
}

.data-table td {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-800);
}

.data-table tr:hover {
  background: var(--gray-50);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Category Badges */
.category-badge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.category-food {
  background: #fef3c7;
  color: #92400e;
}
.category-grocery {
  background: #dcfce7;
  color: #166534;
}
.category-transport {
  background: #dbeafe;
  color: #1e40af;
}
.category-fuel {
  background: #fef3c7;
  color: #a16207;
}
.category-entertainment {
  background: #fce7f3;
  color: #be185d;
}
.category-shopping {
  background: #ecfdf5;
  color: #065f46;
}
.category-bills {
  background: #fef2f2;
  color: #991b1b;
}
.category-healthcare {
  background: #f0f9ff;
  color: #0c4a6e;
}
.category-education {
  background: #f3e8ff;
  color: #6b21a8;
}
.category-travel {
  background: #f0fdf4;
  color: #14532d;
}
.category-other {
  background: var(--gray-200);
  color: var(--gray-700);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-2);
}

.action-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.edit-btn {
  background: var(--warning-color);
  color: var(--white);
}

.edit-btn:hover {
  background: #d97706;
}

.delete-btn {
  background: var(--danger-color);
  color: var(--white);
}

.delete-btn:hover {
  background: #dc2626;
}

/* Recent Transactions */
.recent-transactions {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.recent-transactions h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
}

.transaction-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.transaction-item:hover {
  background: var(--gray-50);
  transform: translateX(4px);
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.transaction-name {
  font-weight: 600;
  color: var(--gray-900);
}

.transaction-category {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.transaction-amount {
  font-weight: 700;
  color: var(--gray-900);
}

.transaction-amount.expense {
  color: var(--danger-color);
}

.transaction-amount.income {
  color: var(--success-color);
}

/* Budget Styles */
.budget-overview {
  margin-bottom: var(--spacing-8);
}

.budget-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.budget-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
}

.budget-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.budget-amount span {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
}

.budget-progress {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--success-color),
    var(--warning-color)
  );
  border-radius: var(--radius-md);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
}

.category-budgets {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-8);
}

.category-budgets h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
}

.budget-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.budget-category-card {
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-5);
  border: 1px solid var(--gray-200);
}

.budget-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.budget-category-name {
  font-weight: 600;
  color: var(--gray-900);
}

.budget-category-amount {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.modal-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
}

.close {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-500);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.close:hover {
  color: var(--gray-900);
}

/* Credit Card Import Styles */
.upload-section {
  margin-bottom: var(--spacing-8);
}

.upload-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  text-align: center;
}

.upload-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
}

.upload-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.upload-card p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-6);
}

.file-upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-6);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.file-upload-area.dragover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  background-opacity: 0.1;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-3);
}

.upload-icon-large {
  font-size: var(--font-size-4xl);
  color: var(--gray-400);
}

.upload-placeholder p {
  font-weight: 600;
  color: var(--gray-700);
  margin: 0;
}

.file-types {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.file-selected {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.file-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-1);
}

.file-name {
  font-weight: 600;
  color: var(--gray-900);
}

.file-size {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.password-section {
  margin-bottom: var(--spacing-6);
}

.processing-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.btn-loader {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.processing-status {
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-align: center;
}

.processing-status.success {
  color: var(--success-color);
}

.processing-status.error {
  color: var(--danger-color);
}

.processing-status.info {
  color: var(--primary-color);
}

/* Preview Section */
.preview-section {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-8);
}

.preview-section h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.preview-section p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-6);
}

.preview-controls {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  flex-wrap: wrap;
}

.category-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  background: var(--white);
  color: var(--gray-800);
}

/* Import History */
.import-history {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.import-history h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-6);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.history-item:hover {
  background: var(--gray-50);
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.history-filename {
  font-weight: 600;
  color: var(--gray-900);
}

.history-date {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.history-stats {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: var(--spacing-6);
  right: var(--spacing-6);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  padding: var(--spacing-4) var(--spacing-6);
  z-index: 3000;
  transform: translateX(400px);
  transition: transform var(--transition-normal);
}

.toast.show {
  transform: translateX(0);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.toast-icon {
  font-size: var(--font-size-lg);
}

.toast-message {
  font-weight: 600;
  color: var(--gray-900);
}

.toast.success .toast-icon {
  color: var(--success-color);
}

.toast.error .toast-icon {
  color: var(--danger-color);
}

.toast.info .toast-icon {
  color: var(--primary-color);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-4);
  }

  .page-header h1 {
    font-size: var(--font-size-2xl);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-4);
  }

  .filter-total-wrapper {
    align-items: flex-start;
    width: 100%;
  }

  .preview-controls {
    flex-direction: column;
  }

  .budget-amount {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-4);
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .budget-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-3);
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
  }

  .transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .action-buttons {
    align-self: flex-end;
  }

  .toast {
    right: var(--spacing-3);
    left: var(--spacing-3);
    transform: translateY(-100px);
  }

  .toast.show {
    transform: translateY(0);
  }
}
