/* Modern CSS Variables for Design System */
:root {
  /* Color Palette */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --success-light: #34d399;
  --danger-color: #ef4444;
  --danger-light: #f87171;
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Background Colors */
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

* {
  box-sizing: border-box;
}

body {
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--gray-100) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  margin: 0;
  padding: var(--spacing-4);
  font-family: var(--font-family);
  color: var(--gray-800);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

/* Typography Improvements */
h1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  letter-spacing: -0.025em;
  margin: 0;
  color: var(--gray-900);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: 600;
  margin: 0 0 var(--spacing-8) 0;
  color: var(--gray-900);
  text-align: center;
}

h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
  margin: var(--spacing-8) 0 var(--spacing-4) 0;
  padding-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--gray-200);
  position: relative;
}

h3::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--primary-color);
}

h4 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--gray-600);
}

/* Three-Card Layout Container */
.three-card-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-6);
  margin: var(--spacing-8) 0;
}

@media (max-width: 768px) {
  .three-card-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .three-card-container {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-5);
  }

  .three-card-container .summary-card:last-child {
    grid-column: 1 / -1;
  }
}

.summary-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  text-align: center;
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all var(--transition-normal);
}

.summary-card.balance::before {
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
}

.summary-card.income::before {
  background: linear-gradient(
    90deg,
    var(--success-color),
    var(--success-light)
  );
}

.summary-card.expense::before {
  background: linear-gradient(90deg, var(--danger-color), var(--danger-light));
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.summary-card h4 {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-3);
}

.money {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  letter-spacing: -0.025em;
  margin: 0;
}

.money-plus {
  color: var(--success-color);
}

.money-minus {
  color: var(--danger-color);
}

/* Form Styling */
.form-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-8);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.form-card:hover {
  box-shadow: var(--shadow-lg);
}

.form-control {
  margin-bottom: var(--spacing-6);
}

/* Form Row Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }
}

label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
  letter-spacing: 0.025em;
}

input[type="text"],
input[type="number"],
input[type="date"],
select {
  width: 100%;
  padding: var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  color: var(--gray-800);
  background: var(--white);
  transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

input[type="text"]:hover,
input[type="number"]:hover,
input[type="date"]:hover,
select:hover {
  border-color: var(--gray-400);
}

input[type="text"]::placeholder,
input[type="number"]::placeholder {
  color: var(--gray-400);
}

/* Select dropdown styling */
select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--spacing-3) center;
  background-size: 16px;
  padding-right: var(--spacing-10);
}

select option {
  padding: var(--spacing-2);
  background: var(--white);
  color: var(--gray-800);
}

/* Button Styling */
.btn {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  font-family: var(--font-family);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.btn:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--primary-color)
  );
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn:focus,
.delete-btn:focus {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 3px rgba(37, 99, 235, 0.2);
}

/* History Header and Filter Controls */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.filter-controls label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin: 0;
  white-space: nowrap;
}

.filter-controls select {
  min-width: 150px;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .history-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }

  .filter-controls select {
    flex: 1;
    min-width: auto;
  }
}

/* Transaction Container and Table */
.transaction-container {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

.expense-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.expense-table th {
  background: var(--gray-50);
  padding: var(--spacing-4);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 2px solid var(--gray-200);
  white-space: nowrap;
}

.expense-table td {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.expense-table tbody tr {
  transition: all var(--transition-fast);
}

.expense-table tbody tr:hover {
  background: var(--gray-50);
}

.expense-table tbody tr:last-child td {
  border-bottom: none;
}

/* Total Display */
.total-display {
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-top: 2px solid var(--gray-200);
  text-align: right;
  font-size: var(--font-size-lg);
  color: var(--gray-800);
}

#totalAmount {
  color: var(--primary-color);
  font-weight: 700;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-2);
}

.edit-btn,
.delete-btn {
  cursor: pointer;
  border: none;
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.edit-btn {
  background: var(--primary-color);
  color: var(--white);
}

.edit-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.delete-btn {
  background: var(--danger-color);
  color: var(--white);
}

.delete-btn:hover {
  background: var(--danger-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Category Badge */
.category-badge {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.category-food {
  background: #fef3c7;
  color: #92400e;
}
.category-transport {
  background: #dbeafe;
  color: #1e40af;
}
.category-entertainment {
  background: #fce7f3;
  color: #be185d;
}
.category-shopping {
  background: #ecfdf5;
  color: #065f46;
}
.category-bills {
  background: #fef2f2;
  color: #991b1b;
}
.category-healthcare {
  background: #f0f9ff;
  color: #0c4a6e;
}
.category-education {
  background: #f3e8ff;
  color: #6b21a8;
}
.category-travel {
  background: #f0fdf4;
  color: #166534;
}
.category-other {
  background: #f1f5f9;
  color: #475569;
}

/* Amount Display */
.amount-display {
  font-weight: 700;
  font-size: var(--font-size-base);
  color: var(--danger-color);
}

/* Date Display */
.date-display {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--gray-500);
}

.empty-state-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.empty-state-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing-2);
}

.empty-state-subtext {
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: var(--spacing-2);
  }

  .container {
    padding: var(--spacing-4);
    max-width: 100%;
  }

  h2 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-6);
  }

  .balance-amount {
    font-size: var(--font-size-3xl);
  }

  .inc-exp-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .summary-card {
    padding: var(--spacing-5);
  }

  .form-card {
    padding: var(--spacing-6);
  }

  .list li {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .transaction-text {
    margin-right: 0;
  }

  .transaction-amount {
    align-self: flex-end;
  }

  .delete-btn {
    position: static;
    transform: none;
    opacity: 1;
    margin-top: var(--spacing-2);
    align-self: flex-end;
  }

  .delete-btn:hover {
    transform: scale(1.05);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--spacing-3);
  }

  h2 {
    font-size: var(--font-size-xl);
  }

  .balance-amount {
    font-size: var(--font-size-2xl);
  }

  .summary-card {
    padding: var(--spacing-4);
  }

  .form-card {
    padding: var(--spacing-5);
  }

  .money {
    font-size: var(--font-size-xl);
  }
}

/* Loading Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Pulse Animation for Balance */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.balance-updated {
  animation: pulse 0.3s ease-in-out;
}

/* Success/Error States */
.success-message {
  background: linear-gradient(
    135deg,
    var(--success-color),
    var(--success-light)
  );
  color: var(--white);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.error-message {
  background: linear-gradient(135deg, var(--danger-color), var(--danger-light));
  color: var(--white);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-md);
}
