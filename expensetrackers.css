/* Modern CSS Variables for Design System */
:root {
  /* Color Palette */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --success-light: #34d399;
  --danger-color: #ef4444;
  --danger-light: #f87171;
  --warning-color: #f59e0b;
  --warning-light: #fbbf24;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Background Colors */
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

* {
  box-sizing: border-box;
}

body {
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--gray-100) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  margin: 0;
  padding: var(--spacing-4);
  font-family: var(--font-family);
  color: var(--gray-800);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-6);
}

/* Typography Improvements */
h1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  letter-spacing: -0.025em;
  margin: 0;
  color: var(--gray-900);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: 600;
  margin: 0 0 var(--spacing-8) 0;
  color: var(--gray-900);
  text-align: center;
}

h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-800);
  margin: var(--spacing-8) 0 var(--spacing-4) 0;
  padding-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--gray-200);
  position: relative;
}

h3::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--primary-color);
}

h4 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--gray-600);
}

/* Balance Display Card */
.balance-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  text-align: center;
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.balance-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.balance-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-2);
}

.balance-amount {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
  letter-spacing: -0.025em;
}

/* Income/Expense Summary Cards */
.inc-exp-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
  margin: var(--spacing-8) 0;
}

.summary-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-6);
  text-align: center;
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all var(--transition-normal);
}

.summary-card.income::before {
  background: linear-gradient(
    90deg,
    var(--success-color),
    var(--success-light)
  );
}

.summary-card.expense::before {
  background: linear-gradient(90deg, var(--danger-color), var(--danger-light));
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.summary-card h4 {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-3);
}

.money {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  letter-spacing: -0.025em;
  margin: 0;
}

.money-plus {
  color: var(--success-color);
}

.money-minus {
  color: var(--danger-color);
}

/* Form Styling */
.form-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-8);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.form-card:hover {
  box-shadow: var(--shadow-lg);
}

.form-control {
  margin-bottom: var(--spacing-6);
}

label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
  letter-spacing: 0.025em;
}

input[type="text"],
input[type="number"] {
  width: 100%;
  padding: var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  color: var(--gray-800);
  background: var(--white);
  transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="number"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

input[type="text"]:hover,
input[type="number"]:hover {
  border-color: var(--gray-400);
}

input[type="text"]::placeholder,
input[type="number"]::placeholder {
  color: var(--gray-400);
}

/* Button Styling */
.btn {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  font-family: var(--font-family);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.btn:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--primary-color)
  );
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn:focus,
.delete-btn:focus {
  outline: none;
  box-shadow: var(--shadow-lg), 0 0 0 3px rgba(37, 99, 235, 0.2);
}

/* Transaction List Styling */
.transaction-container {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
}

.list::-webkit-scrollbar {
  width: 6px;
}

.list::-webkit-scrollbar-track {
  background: var(--gray-100);
}

.list::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 3px;
}

.list::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

.list li {
  background: var(--white);
  color: var(--gray-800);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: var(--spacing-5);
  margin: 0;
  border-bottom: 1px solid var(--gray-100);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.list li:last-child {
  border-bottom: none;
}

.list li:hover {
  background: var(--gray-50);
  transform: translateX(4px);
}

.list li.plus {
  border-left: 4px solid var(--success-color);
}

.list li.minus {
  border-left: 4px solid var(--danger-color);
}

.transaction-text {
  font-weight: 600;
  color: var(--gray-800);
  flex: 1;
  margin-right: var(--spacing-4);
}

.transaction-amount {
  font-weight: 700;
  font-size: var(--font-size-lg);
  letter-spacing: -0.025em;
}

.delete-btn {
  cursor: pointer;
  background: var(--danger-color);
  border: none;
  color: var(--white);
  font-size: var(--font-size-sm);
  font-weight: 600;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-sm);
  position: absolute;
  top: 50%;
  left: -8px;
  transform: translate(-100%, -50%);
  opacity: 0;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.delete-btn:hover {
  background: var(--danger-light);
  transform: translate(-100%, -50%) scale(1.05);
}

.list li:hover .delete-btn {
  opacity: 1;
  left: -12px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-12);
  color: var(--gray-500);
}

.empty-state-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.empty-state-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing-2);
}

.empty-state-subtext {
  font-size: var(--font-size-sm);
  color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: var(--spacing-2);
  }

  .container {
    padding: var(--spacing-4);
    max-width: 100%;
  }

  h2 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-6);
  }

  .balance-amount {
    font-size: var(--font-size-3xl);
  }

  .inc-exp-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .summary-card {
    padding: var(--spacing-5);
  }

  .form-card {
    padding: var(--spacing-6);
  }

  .list li {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .transaction-text {
    margin-right: 0;
  }

  .transaction-amount {
    align-self: flex-end;
  }

  .delete-btn {
    position: static;
    transform: none;
    opacity: 1;
    margin-top: var(--spacing-2);
    align-self: flex-end;
  }

  .delete-btn:hover {
    transform: scale(1.05);
  }
}

@media (max-width: 480px) {
  .container {
    padding: var(--spacing-3);
  }

  h2 {
    font-size: var(--font-size-xl);
  }

  .balance-amount {
    font-size: var(--font-size-2xl);
  }

  .summary-card {
    padding: var(--spacing-4);
  }

  .form-card {
    padding: var(--spacing-5);
  }

  .money {
    font-size: var(--font-size-xl);
  }
}

/* Loading Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Pulse Animation for Balance */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.balance-updated {
  animation: pulse 0.3s ease-in-out;
}

/* Success/Error States */
.success-message {
  background: linear-gradient(
    135deg,
    var(--success-color),
    var(--success-light)
  );
  color: var(--white);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.error-message {
  background: linear-gradient(135deg, var(--danger-color), var(--danger-light));
  color: var(--white);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  font-weight: 600;
  text-align: center;
  box-shadow: var(--shadow-md);
}
